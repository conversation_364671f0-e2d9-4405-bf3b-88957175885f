import React, { useState, useCallback } from 'react'
import { Search, Filter, Music, TrendingUp, Clock, X } from 'lucide-react'
import TrackList from '../components/TrackList'
import MusicPlayer from '../components/MusicPlayer'
import Leaderboards from '../components/Leaderboards'

const DiscoverPage = () => {
  const [currentTrack, setCurrentTrack] = useState(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedGenre, setSelectedGenre] = useState('')
  const [sortBy, setSortBy] = useState('recent')
  const [selectedTags, setSelectedTags] = useState([])
  const [filters, setFilters] = useState({})
  const [refreshKey, setRefreshKey] = useState(0)

  const genres = [
    'All Genres',
    'Electronic',
    'Ambient',
    'Classical',
    'Jazz',
    'Rock',
    'Pop',
    'Hip Hop',
    'Experimental',
    'Other'
  ]

  const sortOptions = [
    { value: 'recent', label: 'Newest First' },
    { value: 'oldest', label: 'Oldest First' },
    { value: 'popular', label: 'Most Popular' },
    { value: 'title', label: 'Alphabetical' }
  ]

  const popularTags = [
    'ambient', 'electronic', 'chill', 'experimental', 'classical',
    'jazz', 'synthwave', 'lo-fi', 'orchestral', 'minimal'
  ]

  const handlePlay = (track) => {
    if (currentTrack?.trackId === track.trackId) {
      setIsPlaying(true)
    } else {
      setCurrentTrack(track)
      setIsPlaying(true)
    }
  }

  const handlePause = () => {
    setIsPlaying(false)
  }

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying)
  }

  const handleTrackEnd = () => {
    setIsPlaying(false)
  }

  // Update filters and refresh track list
  const updateFilters = useCallback(() => {
    const newFilters = {}

    if (searchQuery.trim()) {
      newFilters.search = searchQuery.trim()
    }

    if (selectedGenre && selectedGenre !== 'All Genres') {
      newFilters.genre = selectedGenre
    }

    if (selectedTags.length > 0) {
      newFilters.tags = selectedTags.join(',')
    }

    newFilters.sortBy = sortBy

    setFilters(newFilters)
    setRefreshKey(prev => prev + 1) // Force TrackList to refresh
  }, [searchQuery, selectedGenre, selectedTags, sortBy])

  const handleSearch = (e) => {
    e.preventDefault()
    updateFilters()
  }

  const handleGenreChange = (genre) => {
    setSelectedGenre(genre)
    // Auto-update filters when genre changes
    setTimeout(updateFilters, 0)
  }

  const handleSortChange = (newSortBy) => {
    setSortBy(newSortBy)
    // Auto-update filters when sort changes
    setTimeout(updateFilters, 0)
  }

  const addTag = (tag) => {
    if (!selectedTags.includes(tag)) {
      const newTags = [...selectedTags, tag]
      setSelectedTags(newTags)
      // Auto-update filters when tags change
      setTimeout(updateFilters, 0)
    }
  }

  const removeTag = (tag) => {
    const newTags = selectedTags.filter(t => t !== tag)
    setSelectedTags(newTags)
    // Auto-update filters when tags change
    setTimeout(updateFilters, 0)
  }

  const clearAllFilters = () => {
    setSearchQuery('')
    setSelectedGenre('')
    setSelectedTags([])
    setSortBy('recent')
    setFilters({})
    setRefreshKey(prev => prev + 1)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center gap-3 mb-6">
              <Music className="w-8 h-8 text-blue-500" />
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Discover Music</h1>
                <p className="text-gray-600">Explore AI-generated music from creators around the world</p>
              </div>
            </div>

            {/* Search and Filters */}
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search Bar */}
              <form onSubmit={handleSearch} className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search tracks, artists, or AI tools..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </form>

              {/* Genre Filter */}
              <div className="flex items-center gap-2">
                <Filter className="w-5 h-5 text-gray-400" />
                <select
                  value={selectedGenre}
                  onChange={(e) => handleGenreChange(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {genres.map((genre) => (
                    <option key={genre} value={genre === 'All Genres' ? '' : genre}>
                      {genre}
                    </option>
                  ))}
                </select>
              </div>

              {/* Sort Options */}
              <select
                value={sortBy}
                onChange={(e) => handleSortChange(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {sortOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Active Filters */}
            {(searchQuery || selectedGenre || selectedTags.length > 0) && (
              <div className="mt-4 flex flex-wrap items-center gap-2">
                <span className="text-sm text-gray-600">Active filters:</span>

                {searchQuery && (
                  <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm flex items-center gap-1">
                    Search: "{searchQuery}"
                    <button onClick={() => { setSearchQuery(''); updateFilters(); }}>
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                )}

                {selectedGenre && (
                  <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm flex items-center gap-1">
                    Genre: {selectedGenre}
                    <button onClick={() => handleGenreChange('')}>
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                )}

                {selectedTags.map(tag => (
                  <span key={tag} className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm flex items-center gap-1">
                    #{tag}
                    <button onClick={() => removeTag(tag)}>
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}

                <button
                  onClick={clearAllFilters}
                  className="text-sm text-gray-500 hover:text-gray-700 underline"
                >
                  Clear all
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Featured Section */}
            <div className="mb-8">
              <div className="flex items-center gap-2 mb-4">
                <TrendingUp className="w-5 h-5 text-orange-500" />
                <h2 className="text-xl font-semibold text-gray-900">Trending Now</h2>
              </div>
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
                <h3 className="text-lg font-semibold mb-2">Welcome to Tunami</h3>
                <p className="text-blue-100 mb-4">
                  Discover amazing AI-generated music from talented creators. 
                  Upload your own tracks and join the community!
                </p>
                <div className="flex gap-3">
                  <span className="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm">
                    🎵 100+ Tracks
                  </span>
                  <span className="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm">
                    🤖 AI-Powered
                  </span>
                  <span className="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm">
                    🌟 Community-Driven
                  </span>
                </div>
              </div>
            </div>

            {/* Track List */}
            <div className="bg-white rounded-lg shadow-sm">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center gap-2">
                  <Clock className="w-5 h-5 text-gray-400" />
                  <h2 className="text-lg font-semibold text-gray-900">Latest Tracks</h2>
                </div>
              </div>
              
              <div className="p-6">
                <TrackList
                  key={refreshKey} // Force refresh when filters change
                  filters={filters}
                  currentTrack={currentTrack}
                  isPlaying={isPlaying}
                  onPlay={handlePlay}
                  onPause={handlePause}
                />
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Music Player */}
            {currentTrack && (
              <div className="sticky top-6">
                <MusicPlayer
                  track={currentTrack}
                  isPlaying={isPlaying}
                  onPlayPause={handlePlayPause}
                  onTrackEnd={handleTrackEnd}
                />
              </div>
            )}

            {/* Popular Genres */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Popular Genres</h3>
              <div className="space-y-2">
                {genres.slice(1).map((genre) => (
                  <button
                    key={genre}
                    onClick={() => handleGenreChange(genre)}
                    className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                      selectedGenre === genre
                        ? 'bg-blue-100 text-blue-700'
                        : 'hover:bg-gray-100 text-gray-700'
                    }`}
                  >
                    {genre}
                  </button>
                ))}
              </div>
            </div>

            {/* Popular Tags */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Popular Tags</h3>
              <div className="flex flex-wrap gap-2">
                {popularTags.map((tag) => (
                  <button
                    key={tag}
                    onClick={() => addTag(tag)}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      selectedTags.includes(tag)
                        ? 'bg-purple-100 text-purple-700 cursor-default'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                    disabled={selectedTags.includes(tag)}
                  >
                    #{tag}
                  </button>
                ))}
              </div>
            </div>

            {/* Leaderboards */}
            <Leaderboards />

            {/* AI Tools Spotlight */}
            <div className="bg-gradient-to-br from-purple-50 to-blue-50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">AI Tools Spotlight</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                    <span className="text-white text-xs font-bold">AI</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">AIVA</p>
                    <p className="text-sm text-gray-600">Classical & Orchestral</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                    <span className="text-white text-xs font-bold">AM</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Amper Music</p>
                    <p className="text-sm text-gray-600">Pop & Electronic</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                    <span className="text-white text-xs font-bold">SR</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Soundraw</p>
                    <p className="text-sm text-gray-600">Ambient & Chill</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Community Stats */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Community</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Tracks</span>
                  <span className="font-semibold">1,234</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Active Creators</span>
                  <span className="font-semibold">89</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Plays</span>
                  <span className="font-semibold">45.6K</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DiscoverPage
