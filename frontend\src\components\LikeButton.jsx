import React, { useState } from 'react'
import { Heart } from 'lucide-react'
import { toast } from 'react-hot-toast'
import apiService from '../services/apiService'

const LikeButton = ({ 
  trackId, 
  initialLikeCount = 0, 
  initialIsLiked = false, 
  size = 'md',
  showCount = true,
  className = '' 
}) => {
  const [isLiked, setIsLiked] = useState(initialIsLiked)
  const [likeCount, setLikeCount] = useState(initialLikeCount)
  const [isLoading, setIsLoading] = useState(false)

  // Size variants
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  }

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  }

  const handleLike = async (e) => {
    e.stopPropagation() // Prevent event bubbling
    
    if (isLoading) return

    setIsLoading(true)
    
    // Optimistic update
    const newIsLiked = !isLiked
    const newLikeCount = newIsLiked ? likeCount + 1 : Math.max(likeCount - 1, 0)
    
    setIsLiked(newIsLiked)
    setLikeCount(newLikeCount)

    try {
      const response = await apiService.likeTrack(trackId, newIsLiked ? 'like' : 'unlike')
      
      if (response.success) {
        // Update with server response
        setIsLiked(response.data.isLiked)
        setLikeCount(response.data.likeCount)
        
        toast.success(
          newIsLiked ? '❤️ Track liked!' : '💔 Track unliked',
          { duration: 1500 }
        )
      } else {
        throw new Error(response.error?.message || 'Failed to update like')
      }
    } catch (error) {
      console.error('Like error:', error)
      
      // Revert optimistic update
      setIsLiked(!newIsLiked)
      setLikeCount(likeCount)
      
      toast.error('Failed to update like. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <button
      onClick={handleLike}
      disabled={isLoading}
      className={`
        flex items-center gap-1 px-2 py-1 rounded-full transition-all duration-200
        ${isLiked 
          ? 'text-red-500 bg-red-50 hover:bg-red-100' 
          : 'text-gray-500 hover:text-red-500 hover:bg-red-50'
        }
        ${isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105 active:scale-95'}
        ${className}
      `}
      title={isLiked ? 'Unlike this track' : 'Like this track'}
    >
      <Heart 
        className={`
          ${sizeClasses[size]} transition-all duration-200
          ${isLiked ? 'fill-current' : ''}
          ${isLoading ? 'animate-pulse' : ''}
        `}
      />
      {showCount && (
        <span className={`
          ${textSizeClasses[size]} font-medium transition-all duration-200
          ${isLoading ? 'animate-pulse' : ''}
        `}>
          {likeCount.toLocaleString()}
        </span>
      )}
    </button>
  )
}

export default LikeButton
