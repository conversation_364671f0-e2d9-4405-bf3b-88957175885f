#!/usr/bin/env node

/**
 * Start Local Services Script for Tunami MVP
 * This script starts all required local services for development
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkDockerInstalled() {
  return new Promise((resolve) => {
    exec('docker --version', (error) => {
      resolve(!error);
    });
  });
}

function checkDockerRunning() {
  return new Promise((resolve) => {
    exec('docker info', (error) => {
      resolve(!error);
    });
  });
}

function startDockerServices() {
  return new Promise((resolve, reject) => {
    log('🐳 Starting Docker services...', 'cyan');
    
    const dockerCompose = spawn('docker-compose', [
      '-f', 'docker-compose.local.yml',
      'up', '-d'
    ], {
      stdio: 'inherit',
      cwd: process.cwd()
    });

    dockerCompose.on('close', (code) => {
      if (code === 0) {
        log('✅ Docker services started successfully!', 'green');
        resolve();
      } else {
        reject(new Error(`Docker compose failed with code ${code}`));
      }
    });
  });
}

function waitForServices() {
  return new Promise((resolve) => {
    log('⏳ Waiting for services to be ready...', 'yellow');
    setTimeout(() => {
      log('✅ Services should be ready!', 'green');
      resolve();
    }, 10000); // Wait 10 seconds for services to start
  });
}

function createDynamoDBTables() {
  return new Promise((resolve, reject) => {
    log('📊 Creating DynamoDB tables...', 'cyan');
    
    const createTablesScript = spawn('node', [
      path.join(__dirname, 'create-dynamodb-tables.js')
    ], {
      stdio: 'inherit'
    });

    createTablesScript.on('close', (code) => {
      if (code === 0) {
        log('✅ DynamoDB tables created successfully!', 'green');
        resolve();
      } else {
        log('⚠️  Warning: Some tables might already exist', 'yellow');
        resolve(); // Don't fail if tables already exist
      }
    });
  });
}

function createS3Buckets() {
  return new Promise((resolve, reject) => {
    log('🪣 Creating S3 buckets...', 'cyan');
    
    const createBucketsScript = spawn('node', [
      path.join(__dirname, 'create-s3-buckets.js')
    ], {
      stdio: 'inherit'
    });

    createBucketsScript.on('close', (code) => {
      if (code === 0) {
        log('✅ S3 buckets created successfully!', 'green');
        resolve();
      } else {
        log('⚠️  Warning: Some buckets might already exist', 'yellow');
        resolve(); // Don't fail if buckets already exist
      }
    });
  });
}

async function main() {
  try {
    log('🚀 Starting Tunami MVP Local Development Services', 'cyan');
    log('', 'reset');

    // Check Docker
    const dockerInstalled = await checkDockerInstalled();
    if (!dockerInstalled) {
      log('❌ Docker is not installed. Please install Docker Desktop first.', 'red');
      process.exit(1);
    }

    const dockerRunning = await checkDockerRunning();
    if (!dockerRunning) {
      log('❌ Docker is not running. Please start Docker Desktop first.', 'red');
      process.exit(1);
    }

    // Start Docker services
    await startDockerServices();
    
    // Wait for services to be ready
    await waitForServices();
    
    // Create DynamoDB tables
    await createDynamoDBTables();
    
    // Create S3 buckets
    await createS3Buckets();

    log('', 'reset');
    log('🎉 All local services are ready!', 'green');
    log('', 'reset');
    log('📋 Service URLs:', 'cyan');
    log('• DynamoDB Local: http://localhost:8000', 'yellow');
    log('• DynamoDB Admin: http://localhost:8001', 'yellow');
    log('• LocalStack (S3): http://localhost:4566', 'yellow');
    log('', 'reset');
    log('💡 You can now run: npm run dev:local', 'cyan');

  } catch (error) {
    log(`❌ Error starting local services: ${error.message}`, 'red');
    process.exit(1);
  }
}

main();
