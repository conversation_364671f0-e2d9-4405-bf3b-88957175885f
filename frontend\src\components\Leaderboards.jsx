import React, { useState, useEffect } from 'react'
import { Trophy, TrendingUp, Music, User, Crown, Medal, Award } from 'lucide-react'
import apiService from '../services/apiService'
import LoadingSpinner from './LoadingSpinner'

const Leaderboards = () => {
  const [activeTab, setActiveTab] = useState('tracks')
  const [timeframe, setTimeframe] = useState('weekly')
  const [leaderboardData, setLeaderboardData] = useState({
    tracks: [],
    creators: []
  })
  const [loading, setLoading] = useState(true)

  const tabs = [
    { id: 'tracks', label: 'Top Tracks', icon: Music },
    { id: 'creators', label: 'Top Creators', icon: User }
  ]

  const timeframes = [
    { id: 'daily', label: 'Today' },
    { id: 'weekly', label: 'This Week' },
    { id: 'monthly', label: 'This Month' },
    { id: 'alltime', label: 'All Time' }
  ]

  useEffect(() => {
    loadLeaderboards()
  }, [activeTab, timeframe])

  const loadLeaderboards = async () => {
    setLoading(true)
    try {
      // Mock leaderboard data for now
      const mockData = {
        tracks: [
          {
            rank: 1,
            trackId: 'sample-1',
            title: 'AI Symphony No. 1',
            creator: { username: 'AIComposer', profileImageUrl: '' },
            score: 1250,
            likeCount: 89,
            listenCount: 456,
            commentCount: 23
          },
          {
            rank: 2,
            trackId: 'sample-2',
            title: 'Electronic Dreams',
            creator: { username: 'TechBeats', profileImageUrl: '' },
            score: 980,
            likeCount: 67,
            listenCount: 342,
            commentCount: 18
          },
          {
            rank: 3,
            trackId: 'track-3',
            title: 'Neural Melodies',
            creator: { username: 'DeepMusic', profileImageUrl: '' },
            score: 875,
            likeCount: 54,
            listenCount: 298,
            commentCount: 15
          }
        ],
        creators: [
          {
            rank: 1,
            userId: 'creator-1',
            username: 'AIComposer',
            profileImageUrl: '',
            score: 2340,
            trackCount: 12,
            totalLikes: 234,
            followerCount: 89
          },
          {
            rank: 2,
            userId: 'creator-2',
            username: 'TechBeats',
            profileImageUrl: '',
            score: 1890,
            trackCount: 8,
            totalLikes: 189,
            followerCount: 67
          },
          {
            rank: 3,
            userId: 'creator-3',
            username: 'DeepMusic',
            profileImageUrl: '',
            score: 1456,
            trackCount: 6,
            totalLikes: 145,
            followerCount: 45
          }
        ]
      }
      
      setLeaderboardData(mockData)
    } catch (error) {
      console.error('Load leaderboards error:', error)
    } finally {
      setLoading(false)
    }
  }

  const getRankIcon = (rank) => {
    switch (rank) {
      case 1:
        return <Crown className="w-6 h-6 text-yellow-500" />
      case 2:
        return <Medal className="w-6 h-6 text-gray-400" />
      case 3:
        return <Award className="w-6 h-6 text-amber-600" />
      default:
        return <span className="w-6 h-6 flex items-center justify-center text-gray-500 font-bold">{rank}</span>
    }
  }

  const getRankBadgeColor = (rank) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white'
      case 2:
        return 'bg-gradient-to-r from-gray-300 to-gray-500 text-white'
      case 3:
        return 'bg-gradient-to-r from-amber-400 to-amber-600 text-white'
      default:
        return 'bg-gray-100 text-gray-700'
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-sm">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center gap-3 mb-4">
          <Trophy className="w-6 h-6 text-yellow-500" />
          <h2 className="text-xl font-bold text-gray-900">Leaderboards</h2>
        </div>

        {/* Tabs */}
        <div className="flex gap-1 mb-4">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            )
          })}
        </div>

        {/* Timeframe Selector */}
        <div className="flex gap-2">
          {timeframes.map((tf) => (
            <button
              key={tf.id}
              onClick={() => setTimeframe(tf.id)}
              className={`px-3 py-1 rounded-full text-sm transition-colors ${
                timeframe === tf.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {tf.label}
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {loading ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner />
          </div>
        ) : (
          <div className="space-y-3">
            {activeTab === 'tracks' ? (
              leaderboardData.tracks.map((track) => (
                <div
                  key={track.trackId}
                  className={`flex items-center gap-4 p-4 rounded-lg border transition-colors hover:bg-gray-50 ${
                    track.rank <= 3 ? 'border-yellow-200 bg-yellow-50' : 'border-gray-200'
                  }`}
                >
                  {/* Rank */}
                  <div className={`flex items-center justify-center w-12 h-12 rounded-full ${getRankBadgeColor(track.rank)}`}>
                    {getRankIcon(track.rank)}
                  </div>

                  {/* Track Info */}
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-gray-900 truncate">{track.title}</h3>
                    <p className="text-sm text-gray-600">by {track.creator.username}</p>
                    <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
                      <span>❤️ {track.likeCount}</span>
                      <span>🎵 {track.listenCount}</span>
                      <span>💬 {track.commentCount}</span>
                    </div>
                  </div>

                  {/* Score */}
                  <div className="text-right">
                    <div className="text-lg font-bold text-gray-900">{track.score.toLocaleString()}</div>
                    <div className="text-xs text-gray-500">points</div>
                  </div>
                </div>
              ))
            ) : (
              leaderboardData.creators.map((creator) => (
                <div
                  key={creator.userId}
                  className={`flex items-center gap-4 p-4 rounded-lg border transition-colors hover:bg-gray-50 ${
                    creator.rank <= 3 ? 'border-yellow-200 bg-yellow-50' : 'border-gray-200'
                  }`}
                >
                  {/* Rank */}
                  <div className={`flex items-center justify-center w-12 h-12 rounded-full ${getRankBadgeColor(creator.rank)}`}>
                    {getRankIcon(creator.rank)}
                  </div>

                  {/* Creator Info */}
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                      <User className="w-5 h-5 text-gray-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{creator.username}</h3>
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span>🎵 {creator.trackCount} tracks</span>
                        <span>❤️ {creator.totalLikes} likes</span>
                        <span>👥 {creator.followerCount} followers</span>
                      </div>
                    </div>
                  </div>

                  {/* Score */}
                  <div className="text-right">
                    <div className="text-lg font-bold text-gray-900">{creator.score.toLocaleString()}</div>
                    <div className="text-xs text-gray-500">points</div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default Leaderboards
