# Tunami MVP - Local Development Setup Guide

This guide will help you set up the Tunami MVP application for local development and testing.

## 🛠️ Prerequisites

Before starting, ensure you have the following installed:

- **Node.js 18+** - [Download here](https://nodejs.org/)
- **npm 8+** - Comes with Node.js
- **Docker Desktop** - [Download here](https://www.docker.com/products/docker-desktop/)
- **AWS SAM CLI** - [Installation guide](https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html)
- **Git** - [Download here](https://git-scm.com/)

## 🚀 Quick Start

### 1. Clone and Install Dependencies

```bash
# Clone the repository
git clone https://github.com/Vinodmurkute/tunami-mvp.git
cd tunami-mvp

# Install all dependencies
npm run install:all
```

### 2. Set Up Local Environment

```bash
# Create local environment configuration
npm run setup:local-env
```

This will create:
- Local environment configuration files
- Frontend `.env.local` file
- Backend `.env.local` file

### 3. Start Local Services

```bash
# Start Docker services (DynamoDB Local, LocalStack for S3)
npm run start:local-services
```

This will:
- Start DynamoDB Local on port 8000
- Start LocalStack (S3) on port 4566
- Start DynamoDB Admin UI on port 8001
- Create required DynamoDB tables
- Create required S3 buckets

### 4. Start Development Servers

```bash
# Start both backend and frontend
npm run dev:local
```

Or start them separately:

```bash
# Terminal 1: Start backend (SAM Local)
npm run dev:backend

# Terminal 2: Start frontend (Vite)
npm run dev:frontend
```

## 🌐 Access Points

Once everything is running, you can access:

- **Frontend Application**: http://localhost:5173
- **Backend API**: http://localhost:3001
- **DynamoDB Admin**: http://localhost:8001
- **LocalStack Dashboard**: http://localhost:4566

## 🧪 Test Users

The local environment comes with pre-seeded test users:

| Email | Password | Role |
|-------|----------|------|
| <EMAIL> | TestPass123! | User |
| <EMAIL> | TestPass123! | User |
| <EMAIL> | AdminPass123! | Admin |

## 📊 Local Services

### DynamoDB Local
- **Port**: 8000
- **Admin UI**: http://localhost:8001
- **Tables**: All Tunami tables with `-local` suffix

### LocalStack (S3)
- **Port**: 4566
- **Buckets**: 
  - `tunami-audio-files-local`
  - `tunami-frontend-local`

### Mock Cognito
- In-memory user authentication
- JWT-like tokens for testing
- Pre-seeded test users

## 🔧 Development Commands

```bash
# Setup and start everything
npm run dev:setup

# Start local services only
npm run start:local-services

# Stop local services
npm run stop:local-services

# Start development servers
npm run dev:local

# Run tests
npm run test

# Run linting
npm run lint

# Build for production
npm run build
```

## 🐛 Troubleshooting

### Docker Issues

**Problem**: Docker services won't start
**Solution**: 
1. Ensure Docker Desktop is running
2. Check if ports 8000, 8001, 4566 are available
3. Run: `docker-compose -f docker-compose.local.yml down` then retry

### SAM Local Issues

**Problem**: SAM Local API won't start
**Solution**:
1. Ensure AWS SAM CLI is installed
2. Run: `sam build` first
3. Check if port 3001 is available

### Frontend Issues

**Problem**: Frontend can't connect to backend
**Solution**:
1. Verify backend is running on port 3001
2. Check `.env.local` file in frontend directory
3. Ensure VITE_API_BASE_URL=http://localhost:3001

### DynamoDB Issues

**Problem**: DynamoDB tables not found
**Solution**:
1. Check if DynamoDB Local is running
2. Run: `node scripts/create-dynamodb-tables.js`
3. Verify tables exist at http://localhost:8001

## 📁 Project Structure

```
tunami-mvp/
├── backend/                    # Backend Lambda functions
│   ├── src/
│   │   ├── handlers/          # Lambda function handlers
│   │   ├── services/          # Business logic services
│   │   │   ├── cognitoService.js      # AWS Cognito integration
│   │   │   ├── mockCognitoService.js  # Local mock service
│   │   │   └── dynamoService.js       # DynamoDB integration
│   │   └── utils/             # Utility functions
│   └── .env.local             # Backend local environment
├── frontend/                   # React frontend application
│   ├── src/
│   │   ├── config/api.js      # API configuration
│   │   └── ...
│   └── .env.local             # Frontend local environment
├── scripts/                    # Development scripts
│   ├── setup-local-env.js     # Environment setup
│   ├── start-local-services.js # Start local services
│   ├── create-dynamodb-tables.js # Create DynamoDB tables
│   └── create-s3-buckets.js   # Create S3 buckets
├── environments/local/         # Local environment config
├── docker-compose.local.yml    # Docker services
├── template.local.yaml         # SAM template for local dev
└── README.md
```

## 🔄 Development Workflow

1. **Start Development Session**:
   ```bash
   npm run dev:setup
   ```

2. **Make Changes**: Edit code in `backend/src/` or `frontend/src/`

3. **Test Changes**: 
   - Backend changes require SAM restart
   - Frontend changes auto-reload

4. **Run Tests**:
   ```bash
   npm run test
   ```

5. **Stop Development Session**:
   ```bash
   npm run stop:local-services
   ```

## 📝 Notes

- Local environment uses mock services for AWS Cognito
- DynamoDB Local stores data in `./docker/dynamodb/`
- LocalStack data is stored in `./docker/localstack/`
- All local data is persistent between sessions
- Use `docker-compose down -v` to reset all data

## 🆘 Getting Help

If you encounter issues:

1. Check the troubleshooting section above
2. Verify all prerequisites are installed
3. Check Docker Desktop is running
4. Review the console logs for error messages
5. Create an issue in the repository with detailed error information
