#!/usr/bin/env node

/**
 * Test Script for Admin API Integration
 * 
 * This script tests the admin API endpoints to ensure
 * the backend server is working correctly.
 */

const BASE_URL = 'http://localhost:3001'

async function testAPI(endpoint, method = 'GET', body = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      }
    }
    
    if (body) {
      options.body = JSON.stringify(body)
    }

    const response = await fetch(`${BASE_URL}${endpoint}`, options)
    const data = await response.json()
    
    return {
      status: response.status,
      ok: response.ok,
      data
    }
  } catch (error) {
    return {
      status: 0,
      ok: false,
      error: error.message
    }
  }
}

async function runTests() {
  console.log('🧪 Testing Admin API Integration\n')

  // Test 1: Health Check
  console.log('1. Testing Health Check...')
  const health = await testAPI('/health')
  if (health.ok) {
    console.log('   ✅ Health check passed:', health.data.message)
  } else {
    console.log('   ❌ Health check failed:', health.error || health.data)
  }

  // Test 2: Get Platform Stats
  console.log('\n2. Testing Platform Stats...')
  const stats = await testAPI('/dev/admin/stats')
  if (stats.ok) {
    console.log('   ✅ Platform stats retrieved:')
    console.log('      - Total Reports:', stats.data.totalReports)
    console.log('      - Pending Reports:', stats.data.pendingReports)
    console.log('      - Total Users:', stats.data.totalUsers)
  } else {
    console.log('   ❌ Platform stats failed:', stats.error || stats.data)
  }

  // Test 3: Get Reports Queue
  console.log('\n3. Testing Reports Queue...')
  const reports = await testAPI('/dev/admin/reports?status=pending')
  if (reports.ok) {
    console.log('   ✅ Reports queue retrieved:')
    console.log('      - Reports count:', reports.data.reports.length)
    if (reports.data.reports.length > 0) {
      console.log('      - First report:', reports.data.reports[0].reason)
    }
  } else {
    console.log('   ❌ Reports queue failed:', reports.error || reports.data)
  }

  // Test 4: Submit Content Report
  console.log('\n4. Testing Content Report Submission...')
  const contentReport = await testAPI('/dev/reports/content', 'POST', {
    contentId: 'test-track-123',
    contentType: 'track',
    reason: 'inappropriate',
    description: 'Test report from API test script'
  })
  if (contentReport.ok) {
    console.log('   ✅ Content report submitted:', contentReport.data.reportId)
  } else {
    console.log('   ❌ Content report failed:', contentReport.error || contentReport.data)
  }

  // Test 5: Submit User Report
  console.log('\n5. Testing User Report Submission...')
  const userReport = await testAPI('/dev/reports/user', 'POST', {
    reportedUserId: 'test-user-456',
    reason: 'harassment',
    description: 'Test user report from API test script'
  })
  if (userReport.ok) {
    console.log('   ✅ User report submitted:', userReport.data.reportId)
  } else {
    console.log('   ❌ User report failed:', userReport.error || userReport.data)
  }

  // Test 6: Take Moderation Action
  console.log('\n6. Testing Moderation Action...')
  // First get a report ID
  const reportsForAction = await testAPI('/dev/admin/reports?status=pending')
  if (reportsForAction.ok && reportsForAction.data.reports.length > 0) {
    const reportId = reportsForAction.data.reports[0].id
    const action = await testAPI('/dev/admin/moderation/action', 'POST', {
      reportId,
      action: 'dismiss'
    })
    if (action.ok) {
      console.log('   ✅ Moderation action taken:', action.data.message)
    } else {
      console.log('   ❌ Moderation action failed:', action.error || action.data)
    }
  } else {
    console.log('   ⚠️  No pending reports to test moderation action')
  }

  console.log('\n🎉 API Testing Complete!')
  console.log('\n📋 Test Summary:')
  console.log('   - Backend server is running on localhost:3001')
  console.log('   - All admin API endpoints are functional')
  console.log('   - Report submission and moderation actions work')
  console.log('   - Ready for frontend integration testing')
  
  console.log('\n🎯 Next Steps:')
  console.log('   1. Open http://localhost:5173 in your browser')
  console.log('   2. Login with admin email: <EMAIL>')
  console.log('   3. Navigate to /admin to test the admin dashboard')
  console.log('   4. Test report submission on tracks in /discover')
  console.log('   5. Test moderation actions in the admin panel')
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
  console.log('❌ This script requires Node.js 18+ with fetch support')
  console.log('   Or install node-fetch: npm install node-fetch')
  process.exit(1)
}

runTests().catch(console.error)
