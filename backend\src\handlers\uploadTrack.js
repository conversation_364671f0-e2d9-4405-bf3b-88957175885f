const { S3Client, PutObjectCommand, HeadObjectCommand } = require('@aws-sdk/client-s3')
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner')
const { v4: uuidv4 } = require('uuid')
const { successResponse, errorResponse } = require('../utils/responseHelper')
const { validateInput } = require('../utils/validation')
const Joi = require('joi')

const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  maxAttempts: 3, // Enable retry logic
  retryMode: 'adaptive'
})

// Enhanced validation schema for upload track request
const uploadTrackSchema = Joi.object({
  fileName: Joi.string().required().max(255).pattern(/^[^<>:"/\\|?*]+$/), // Prevent invalid file names
  fileSize: Joi.number().required().min(1024).max(50 * 1024 * 1024), // 1KB min, 50MB max
  contentType: Joi.string().required().valid('audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/m4a'),
  duration: Joi.number().optional().max(600) // Optional duration check (10 minutes max)
})

/**
 * Validates file extension against content type
 */
const validateFileExtension = (fileName, contentType) => {
  const extension = fileName.split('.').pop().toLowerCase()
  const validExtensions = {
    'audio/mpeg': ['mp3'],
    'audio/mp3': ['mp3'],
    'audio/wav': ['wav'],
    'audio/m4a': ['m4a']
  }

  return validExtensions[contentType]?.includes(extension)
}

/**
 * Generates a secure, unique file key for S3
 */
const generateFileKey = (userId, fileName) => {
  const fileExtension = fileName.split('.').pop().toLowerCase()
  const uniqueFileName = `${uuidv4()}.${fileExtension}`
  const timestamp = new Date().toISOString().split('T')[0] // YYYY-MM-DD
  return `tracks/${userId}/${timestamp}/${uniqueFileName}`
}

/**
 * Lambda handler for uploading track files to S3
 * Generates presigned URL for secure file upload
 */
exports.handler = async (event) => {
  console.log('Upload track request:', JSON.stringify(event, null, 2))

  try {
    // Extract user ID from Cognito authorizer
    const userId = event.requestContext?.authorizer?.claims?.sub
    if (!userId) {
      console.error('Authentication failed: No user ID found')
      return errorResponse('User not authenticated', 401, 'UNAUTHORIZED')
    }

    // Parse and validate request body
    let body
    try {
      body = JSON.parse(event.body || '{}')
    } catch (parseError) {
      console.error('JSON parse error:', parseError)
      return errorResponse('Invalid JSON in request body', 400, 'INVALID_JSON')
    }

    const validation = validateInput(body, uploadTrackSchema)

    if (!validation.isValid) {
      console.error('Validation failed:', validation.errors)
      return errorResponse('Invalid input data', 400, 'VALIDATION_ERROR', validation.errors)
    }

    const { fileName, fileSize, contentType, duration } = validation.value

    // Additional file extension validation
    if (!validateFileExtension(fileName, contentType)) {
      return errorResponse('File extension does not match content type', 400, 'INVALID_FILE_TYPE')
    }

    // Generate unique file key with organized structure
    const fileKey = generateFileKey(userId, fileName)

    // Enhanced metadata for S3 object
    const metadata = {
      'original-filename': fileName,
      'uploaded-by': userId,
      'upload-timestamp': new Date().toISOString(),
      'file-size': fileSize.toString(),
      'content-type': contentType
    }

    // Add duration to metadata if provided
    if (duration) {
      metadata['duration-seconds'] = duration.toString()
    }

    // Create presigned URL for upload with enhanced configuration
    const putObjectCommand = new PutObjectCommand({
      Bucket: process.env.AUDIO_BUCKET_NAME,
      Key: fileKey,
      ContentType: contentType,
      ContentLength: fileSize,
      Metadata: metadata,
      // Add server-side encryption
      ServerSideEncryption: 'AES256',
      // Add cache control for better CDN performance
      CacheControl: 'max-age=31536000' // 1 year
    })

    // Generate presigned URL (valid for 15 minutes)
    let presignedUrl
    try {
      presignedUrl = await getSignedUrl(s3Client, putObjectCommand, {
        expiresIn: 900 // 15 minutes
      })
    } catch (s3Error) {
      console.error('S3 presigned URL generation failed:', s3Error)
      return errorResponse('Failed to generate upload URL', 500, 'S3_ERROR', {
        details: s3Error.message
      })
    }

    // Generate the final S3 URL for the uploaded file (via CloudFront if available)
    const s3Url = process.env.CLOUDFRONT_DOMAIN
      ? `https://${process.env.CLOUDFRONT_DOMAIN}/${fileKey}`
      : `https://${process.env.AUDIO_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${fileKey}`

    // Log successful operation
    console.log(`Presigned URL generated for user ${userId}, file: ${fileName}, key: ${fileKey}`)

    return successResponse({
      uploadUrl: presignedUrl,
      fileKey: fileKey,
      s3Url: s3Url,
      expiresIn: 900,
      maxFileSize: 50 * 1024 * 1024,
      supportedFormats: ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/m4a']
    }, 'Presigned URL generated successfully')

  } catch (error) {
    console.error('Upload track error:', error)

    // Enhanced error handling with specific error types
    if (error.name === 'ValidationError') {
      return errorResponse('Invalid input data', 400, 'VALIDATION_ERROR', error.details)
    }

    if (error.name === 'AccessDenied') {
      return errorResponse('Access denied to S3 bucket', 403, 'ACCESS_DENIED')
    }

    if (error.name === 'NoSuchBucket') {
      return errorResponse('S3 bucket not found', 500, 'BUCKET_NOT_FOUND')
    }

    return errorResponse('Failed to generate upload URL', 500, 'UPLOAD_ERROR', {
      details: error.message
    })
  }
}
