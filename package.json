{"name": "tunami-mvp", "version": "1.0.0", "description": "Tunami AI Music Platform MVP - A dedicated platform for AI-generated music", "private": true, "scripts": {"install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test -- --run", "test:coverage": "npm run test:coverage:backend && npm run test:coverage:frontend", "test:coverage:backend": "cd backend && npm run test:coverage", "test:coverage:frontend": "cd frontend && npm run test:coverage", "build": "npm run build:backend && npm run build:frontend", "build:backend": "sam build", "build:frontend": "cd frontend && npm run build", "dev:backend": "node scripts/local-express-server.js", "dev:frontend": "cd frontend && npm run dev", "dev:local": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:setup": "npm run setup:local-env && npm run dev:local", "setup:local-env": "node scripts/setup-local-env.js", "start:local-services": "node scripts/start-local-services.js", "stop:local-services": "docker-compose -f docker-compose.local.yml down", "dev:full": "npm run start:local-services && npm run dev:local", "dev:simple": "node scripts/start-simple-local.js", "dev": "node scripts/simple-dev-server.js", "deploy:dev": "bash scripts/deploy.sh dev all", "deploy:staging": "bash scripts/deploy.sh staging all", "deploy:prod": "bash scripts/deploy.sh prod all", "deploy:backend:dev": "bash scripts/deploy.sh dev backend", "deploy:frontend:dev": "bash scripts/deploy.sh dev frontend", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "lint:fix": "npm run lint:fix:backend && npm run lint:fix:frontend", "lint:fix:backend": "cd backend && npm run lint:fix", "lint:fix:frontend": "cd frontend && npm run lint:fix", "clean": "rm -rf backend/node_modules frontend/node_modules .aws-sam", "setup": "npm run install:all && npm run build", "validate": "npm run lint && npm run test && npm run build"}, "repository": {"type": "git", "url": "https://github.com/your-username/tunami-mvp.git"}, "keywords": ["ai-music", "music-platform", "serverless", "aws", "react", "nodejs", "mvp"], "author": "Tunami Team", "license": "PROPRIETARY", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "workspaces": ["backend", "frontend"], "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "wait-on": "^7.2.0"}, "config": {"aws-region": "us-east-1", "node-version": "18"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.821.0", "@aws-sdk/client-s3": "^3.821.0", "axios": "^1.6.0", "cors": "^2.8.5", "express": "^4.21.2", "form-data": "^4.0.2", "multer": "^2.0.0", "uuid": "^11.1.0"}}