import React, { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { useForm } from 'react-hook-form'
import { Upload, Music, X, Plus, Loader2 } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import apiService from '../services/apiService'
import toast from 'react-hot-toast'

const TrackUpload = ({ onUploadSuccess, onClose }) => {
  const [uploadStep, setUploadStep] = useState('select') // select, uploading, metadata, complete
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploadedFile, setUploadedFile] = useState(null)
  const [uploadData, setUploadData] = useState(null)
  const { user } = useAuth()

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    setValue
  } = useForm({
    defaultValues: {
      title: '',
      genre: '',
      description: '',
      aiToolsUsed: [],
      isPublic: true,
      tags: []
    }
  })

  // File upload with drag and drop
  const onDrop = useCallback(async (acceptedFiles) => {
    const file = acceptedFiles[0]
    if (!file) return

    // Enhanced file validation
    const validTypes = ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/m4a']
    if (!validTypes.includes(file.type) && !file.name.match(/\.(mp3|wav|m4a)$/i)) {
      toast.error('Please upload a valid audio file (MP3, WAV, or M4A)')
      return
    }

    if (file.size < 1024) { // 1KB minimum
      toast.error('File is too small. Please upload a valid audio file.')
      return
    }

    if (file.size > 50 * 1024 * 1024) { // 50MB
      toast.error('File size must be less than 50MB')
      return
    }

    setUploadedFile(file)
    setUploadStep('uploading')

    try {
      // Get presigned URL
      const uploadResponse = await apiService.uploadTrack({
        fileName: file.name,
        fileSize: file.size,
        contentType: file.type
      })

      if (!uploadResponse.success) {
        throw new Error(uploadResponse.error?.message || 'Failed to get upload URL')
      }

      const { uploadUrl, fileKey, s3Url } = uploadResponse.data

      // Upload file to our backend
      const formData = new FormData()
      formData.append('file', file)

      const uploadRequest = new XMLHttpRequest()

      uploadRequest.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
          const progress = Math.round((e.loaded / e.total) * 100)
          setUploadProgress(progress)
        }
      })

      uploadRequest.addEventListener('load', () => {
        if (uploadRequest.status === 200) {
          setUploadData({ fileKey, s3Url })
          setUploadStep('metadata')
          // Auto-fill title from filename
          const titleFromFile = file.name.replace(/\.[^/.]+$/, "")
          setValue('title', titleFromFile)
          toast.success('File uploaded successfully!')
        } else {
          throw new Error('Upload failed')
        }
      })

      uploadRequest.addEventListener('error', () => {
        throw new Error('Upload failed')
      })

      uploadRequest.open('PUT', uploadUrl)
      // Don't set Content-Type header for FormData - let browser set it with boundary
      uploadRequest.send(formData)

    } catch (error) {
      console.error('Upload error:', error)
      toast.error(error.message || 'Upload failed')
      setUploadStep('select')
      setUploadedFile(null)
      setUploadProgress(0)
    }
  }, [setValue])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'audio/*': ['.mp3', '.wav', '.m4a']
    },
    maxFiles: 1,
    disabled: uploadStep !== 'select'
  })

  // Handle metadata submission
  const onSubmitMetadata = async (data) => {
    try {
      const metadataResponse = await apiService.createTrackMetadata({
        ...data,
        audioFileUrl: uploadData.s3Url,
        fileKey: uploadData.fileKey,
        aiToolsUsed: data.aiToolsUsed.filter(tool => tool.trim()),
        tags: data.tags.filter(tag => tag.trim()),
        fileSize: uploadedFile?.size,
        // Duration will be extracted by backend from S3 metadata if available
      })

      if (!metadataResponse.success) {
        throw new Error(metadataResponse.error?.message || 'Failed to save track metadata')
      }

      setUploadStep('complete')
      toast.success('Track uploaded successfully!')
      
      if (onUploadSuccess) {
        onUploadSuccess(metadataResponse.data)
      }

      // Auto-close after 2 seconds
      setTimeout(() => {
        if (onClose) onClose()
      }, 2000)

    } catch (error) {
      console.error('Metadata error:', error)
      toast.error(error.message || 'Failed to save track metadata')
    }
  }

  // Add/remove AI tools
  const aiToolsUsed = watch('aiToolsUsed') || []
  const addAiTool = () => {
    setValue('aiToolsUsed', [...aiToolsUsed, ''])
  }
  const removeAiTool = (index) => {
    setValue('aiToolsUsed', aiToolsUsed.filter((_, i) => i !== index))
  }

  // Add/remove tags
  const tags = watch('tags') || []
  const addTag = () => {
    setValue('tags', [...tags, ''])
  }
  const removeTag = (index) => {
    setValue('tags', tags.filter((_, i) => i !== index))
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-2xl font-bold text-gray-900">Upload Track</h2>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-6 h-6" />
            </button>
          )}
        </div>

        {/* Content */}
        <div className="p-6">
          {uploadStep === 'select' && (
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                isDragActive
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
            >
              <input {...getInputProps()} />
              <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-lg font-medium text-gray-900 mb-2">
                {isDragActive ? 'Drop your track here' : 'Upload your AI-generated music'}
              </p>
              <p className="text-gray-500">
                Drag and drop an MP3 file, or click to browse
              </p>
              <p className="text-sm text-gray-400 mt-2">
                Maximum file size: 50MB
              </p>
            </div>
          )}

          {uploadStep === 'uploading' && (
            <div className="text-center py-8">
              <Music className="w-16 h-16 text-blue-500 mx-auto mb-4 animate-pulse" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Uploading {uploadedFile?.name}
              </h3>
              <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                />
              </div>
              <p className="text-gray-500">{uploadProgress}% complete</p>
            </div>
          )}

          {uploadStep === 'metadata' && (
            <form onSubmit={handleSubmit(onSubmitMetadata)} className="space-y-6">
              {/* Basic Info */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Track Title *
                </label>
                <input
                  {...register('title', { required: 'Title is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter track title"
                />
                {errors.title && (
                  <p className="text-red-500 text-sm mt-1">{errors.title.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Genre *
                </label>
                <select
                  {...register('genre', { required: 'Genre is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select genre</option>
                  <option value="Electronic">Electronic</option>
                  <option value="Ambient">Ambient</option>
                  <option value="Classical">Classical</option>
                  <option value="Jazz">Jazz</option>
                  <option value="Rock">Rock</option>
                  <option value="Pop">Pop</option>
                  <option value="Hip Hop">Hip Hop</option>
                  <option value="Experimental">Experimental</option>
                  <option value="Other">Other</option>
                </select>
                {errors.genre && (
                  <p className="text-red-500 text-sm mt-1">{errors.genre.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  {...register('description')}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Describe your track..."
                />
              </div>

              {/* AI Tools Used */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  AI Tools Used
                </label>
                {aiToolsUsed.map((tool, index) => (
                  <div key={index} className="flex gap-2 mb-2">
                    <input
                      {...register(`aiToolsUsed.${index}`)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., AIVA, Amper Music, Soundraw"
                    />
                    <button
                      type="button"
                      onClick={() => removeAiTool(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <X className="w-5 h-5" />
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addAiTool}
                  className="flex items-center gap-2 text-blue-500 hover:text-blue-700"
                >
                  <Plus className="w-4 h-4" />
                  Add AI Tool
                </button>
              </div>

              {/* Tags */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tags
                </label>
                {tags.map((tag, index) => (
                  <div key={index} className="flex gap-2 mb-2">
                    <input
                      {...register(`tags.${index}`)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., ambient, chill, electronic"
                    />
                    <button
                      type="button"
                      onClick={() => removeTag(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <X className="w-5 h-5" />
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addTag}
                  className="flex items-center gap-2 text-blue-500 hover:text-blue-700"
                >
                  <Plus className="w-4 h-4" />
                  Add Tag
                </button>
              </div>

              {/* Privacy Setting */}
              <div className="flex items-center">
                <input
                  {...register('isPublic')}
                  type="checkbox"
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <label className="ml-2 text-sm text-gray-700">
                  Make this track public
                </label>
              </div>

              {/* Submit Button */}
              <div className="flex gap-3">
                <button
                  type="button"
                  onClick={() => setUploadStep('select')}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  Back
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 flex items-center justify-center gap-2"
                >
                  {isSubmitting && <Loader2 className="w-4 h-4 animate-spin" />}
                  Save Track
                </button>
              </div>
            </form>
          )}

          {uploadStep === 'complete' && (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Music className="w-8 h-8 text-green-500" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Track uploaded successfully!
              </h3>
              <p className="text-gray-500">
                Your track is now available on Tunami
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default TrackUpload
