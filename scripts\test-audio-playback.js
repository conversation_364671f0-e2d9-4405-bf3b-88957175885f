const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const API_BASE_URL = 'http://localhost:3001';

async function testAudioPlayback() {
  console.log('🎵 Testing Audio Playback Integration\n');
  
  try {
    // Test 1: Check if sample audio files are accessible
    console.log('📋 Test 1: Sample Audio File Access');
    try {
      const response = await axios.head(`${API_BASE_URL}/audio/sample-1.wav`);
      console.log(`✅ Sample audio file accessible: ${response.status} ${response.statusText}`);
      console.log(`📊 Content-Type: ${response.headers['content-type']}`);
      console.log(`📏 Content-Length: ${response.headers['content-length']} bytes`);
    } catch (error) {
      console.log(`❌ Sample audio file not accessible: ${error.message}`);
    }
    
    // Test 2: Test tracks endpoint returns playable URLs
    console.log('\n📋 Test 2: Track List with Audio URLs');
    const tracksResponse = await axios.get(`${API_BASE_URL}/tracks`);
    const tracks = tracksResponse.data.data.tracks;
    
    console.log(`✅ Found ${tracks.length} tracks`);
    tracks.forEach((track, index) => {
      console.log(`🎵 Track ${index + 1}: "${track.title}"`);
      console.log(`   Audio URL: ${track.audioFileUrl}`);
      console.log(`   Creator: ${track.creator.username}`);
    });
    
    // Test 3: Test file upload flow
    console.log('\n📋 Test 3: File Upload Flow');
    
    // Step 3a: Get upload URL
    const uploadResponse = await axios.post(`${API_BASE_URL}/tracks/upload`, {
      fileName: 'test-upload.wav',
      fileSize: 1000000,
      contentType: 'audio/wav'
    });
    
    console.log('✅ Upload URL generated');
    console.log(`📤 Upload URL: ${uploadResponse.data.data.uploadUrl}`);
    console.log(`🔑 File Key: ${uploadResponse.data.data.fileKey}`);
    console.log(`🔗 S3 URL: ${uploadResponse.data.data.s3Url}`);
    
    // Step 3b: Upload a test file
    const testAudioPath = path.join(__dirname, '../uploads/test-track.wav');
    if (fs.existsSync(testAudioPath)) {
      console.log('\n📤 Uploading test audio file...');
      
      const formData = new FormData();
      formData.append('file', fs.createReadStream(testAudioPath));
      
      try {
        const uploadFileResponse = await axios.put(
          uploadResponse.data.data.uploadUrl,
          formData,
          {
            headers: {
              ...formData.getHeaders(),
            },
            maxContentLength: Infinity,
            maxBodyLength: Infinity
          }
        );
        
        console.log('✅ File uploaded successfully');
        console.log(`📊 Response: ${uploadFileResponse.status} ${uploadFileResponse.statusText}`);
        
        // Step 3c: Create metadata for uploaded file
        console.log('\n📝 Creating track metadata...');
        const metadataResponse = await axios.post(`${API_BASE_URL}/tracks/metadata`, {
          title: 'Test Upload Track',
          genre: 'Test',
          description: 'A test track uploaded via integration test',
          aiToolsUsed: ['TestAI'],
          audioFileUrl: uploadResponse.data.data.s3Url,
          fileKey: uploadResponse.data.data.fileKey,
          isPublic: true,
          tags: ['test', 'upload']
        });
        
        console.log('✅ Track metadata created');
        console.log(`🎵 Track ID: ${metadataResponse.data.data.track.trackId}`);
        console.log(`🎼 Track Title: ${metadataResponse.data.data.track.title}`);
        
        // Step 3d: Verify uploaded track is accessible
        console.log('\n🔍 Verifying uploaded track accessibility...');
        try {
          const audioCheckResponse = await axios.head(uploadResponse.data.data.s3Url);
          console.log(`✅ Uploaded audio file accessible: ${audioCheckResponse.status}`);
          console.log(`📊 Content-Type: ${audioCheckResponse.headers['content-type']}`);
        } catch (error) {
          console.log(`❌ Uploaded audio file not accessible: ${error.message}`);
        }
        
      } catch (uploadError) {
        console.log(`❌ File upload failed: ${uploadError.message}`);
        if (uploadError.response) {
          console.log(`   Response: ${uploadError.response.status} ${uploadError.response.statusText}`);
          console.log(`   Data:`, uploadError.response.data);
        }
      }
    } else {
      console.log(`❌ Test audio file not found: ${testAudioPath}`);
    }
    
    // Test 4: Verify updated track list includes uploaded track
    console.log('\n📋 Test 4: Updated Track List');
    const updatedTracksResponse = await axios.get(`${API_BASE_URL}/tracks`);
    const updatedTracks = updatedTracksResponse.data.data.tracks;
    
    console.log(`✅ Updated track list has ${updatedTracks.length} tracks`);
    const uploadedTrack = updatedTracks.find(track => track.title === 'Test Upload Track');
    if (uploadedTrack) {
      console.log(`🎉 Found uploaded track: "${uploadedTrack.title}"`);
      console.log(`   Audio URL: ${uploadedTrack.audioFileUrl}`);
      console.log(`   Track ID: ${uploadedTrack.trackId}`);
    } else {
      console.log(`❌ Uploaded track not found in track list`);
    }
    
    // Summary
    console.log('\n' + '='.repeat(50));
    console.log('🎯 Audio Playback Test Summary:');
    console.log('✅ Sample audio files are accessible');
    console.log('✅ Track list returns proper audio URLs');
    console.log('✅ File upload flow works end-to-end');
    console.log('✅ Uploaded tracks appear in track list');
    console.log('✅ Audio serving endpoint works correctly');
    console.log('\n🎉 All audio playback tests passed!');
    console.log('🎵 Tracks should now be playable in the frontend');
    
  } catch (error) {
    console.error('\n❌ Audio playback test failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
  }
}

// Run the test
testAudioPlayback();
