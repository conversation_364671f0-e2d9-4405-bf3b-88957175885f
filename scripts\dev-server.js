#!/usr/bin/env node

/**
 * Professional Development Server for Tunami MVP
 * Robust error handling, proper logging, and reliable startup
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const http = require('http');

// Configuration
const CONFIG = {
  backend: {
    port: 3001,
    script: path.join(__dirname, 'local-express-server.js'),
    healthPath: '/health'
  },
  frontend: {
    port: 5173,
    cwd: path.join(process.cwd(), 'frontend'),
    command: process.platform === 'win32' ? 'npm.cmd' : 'npm',
    args: ['run', 'dev']
  },
  timeouts: {
    startup: 30000,
    healthCheck: 5000,
    shutdown: 10000
  }
};

// Professional logging system
class Logger {
  static colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
  };

  static log(level, message, service = '') {
    const timestamp = new Date().toISOString();
    const color = this.colors[level] || this.colors.reset;
    const prefix = service ? `[${service}]` : '';
    console.log(`${color}[${timestamp}] ${prefix} ${message}${this.colors.reset}`);
  }

  static info(message, service) { this.log('blue', message, service); }
  static success(message, service) { this.log('green', message, service); }
  static warn(message, service) { this.log('yellow', message, service); }
  static error(message, service) { this.log('red', message, service); }
  static debug(message, service) { this.log('cyan', message, service); }
}

// Health check utility
class HealthChecker {
  static async checkService(port, path = '/', timeout = 5000) {
    return new Promise((resolve) => {
      const req = http.get(`http://localhost:${port}${path}`, { timeout }, (res) => {
        resolve(res.statusCode === 200);
      });

      req.on('error', () => resolve(false));
      req.on('timeout', () => {
        req.destroy();
        resolve(false);
      });
    });
  }

  static async waitForService(name, port, path = '/', maxWait = 30000) {
    const startTime = Date.now();
    const checkInterval = 1000;

    Logger.info(`Waiting for ${name} to be ready...`, 'HEALTH');

    while (Date.now() - startTime < maxWait) {
      if (await this.checkService(port, path)) {
        Logger.success(`${name} is ready!`, 'HEALTH');
        return true;
      }
      await new Promise(resolve => setTimeout(resolve, checkInterval));
    }

    throw new Error(`${name} failed to start within ${maxWait}ms`);
  }
}

// Process manager with robust error handling
class ProcessManager {
  constructor() {
    this.processes = new Map();
    this.isShuttingDown = false;
    this.setupSignalHandlers();
  }

  setupSignalHandlers() {
    const shutdown = async (signal) => {
      if (this.isShuttingDown) return;
      this.isShuttingDown = true;

      Logger.warn(`Received ${signal}, shutting down gracefully...`, 'SYSTEM');
      await this.shutdown();
      process.exit(0);
    };

    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('uncaughtException', (error) => {
      Logger.error(`Uncaught exception: ${error.message}`, 'SYSTEM');
      Logger.error(error.stack, 'SYSTEM');
      this.shutdown().then(() => process.exit(1));
    });
  }

  async startProcess(name, command, args = [], options = {}) {
    try {
      Logger.info(`Starting ${name}...`, 'PROCESS');

      const proc = spawn(command, args, {
        stdio: ['ignore', 'pipe', 'pipe'],
        shell: process.platform === 'win32',
        ...options
      });

      // Wait a moment for the process to initialize
      await new Promise(resolve => setTimeout(resolve, 100));

      if (!proc || !proc.pid) {
        throw new Error(`Failed to start ${name}: Process not created or no PID assigned`);
      }

      this.processes.set(name, proc);

      // Handle stdout
      proc.stdout.on('data', (data) => {
        const output = data.toString().trim();
        if (output) {
          output.split('\n').forEach(line => {
            if (line.trim()) Logger.info(line, name);
          });
        }
      });

      // Handle stderr
      proc.stderr.on('data', (data) => {
        const output = data.toString().trim();
        if (output) {
          output.split('\n').forEach(line => {
            if (line.trim()) Logger.error(line, name);
          });
        }
      });

      // Handle process events
      proc.on('close', (code, signal) => {
        this.processes.delete(name);
        if (code !== 0 && !this.isShuttingDown) {
          Logger.error(`${name} exited with code ${code} (signal: ${signal})`, 'PROCESS');
        } else {
          Logger.info(`${name} stopped`, 'PROCESS');
        }
      });

      proc.on('error', (error) => {
        Logger.error(`${name} error: ${error.message}`, 'PROCESS');
        this.processes.delete(name);
      });

      Logger.success(`${name} started with PID ${proc.pid}`, 'PROCESS');
      return proc;

    } catch (error) {
      Logger.error(`Failed to start ${name}: ${error.message}`, 'PROCESS');
      throw error;
    }
  }

  async shutdown() {
    Logger.info('Shutting down all processes...', 'SYSTEM');

    const shutdownPromises = Array.from(this.processes.entries()).map(([name, proc]) => {
      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          Logger.warn(`Force killing ${name}...`, 'PROCESS');
          try {
            proc.kill('SIGKILL');
          } catch (error) {
            // Process might already be dead
          }
          resolve();
        }, CONFIG.timeouts.shutdown);

        proc.on('close', () => {
          clearTimeout(timeout);
          resolve();
        });

        try {
          proc.kill('SIGTERM');
        } catch (error) {
          clearTimeout(timeout);
          resolve();
        }
      });
    });

    await Promise.all(shutdownPromises);
    this.processes.clear();
    Logger.success('All processes shut down', 'SYSTEM');
  }
}

// Main development server class
class DevelopmentServer {
  constructor() {
    this.processManager = new ProcessManager();
  }

  async validateEnvironment() {
    Logger.info('Validating environment...', 'SETUP');

    // Check if required files exist
    const requiredFiles = [
      CONFIG.backend.script,
      path.join(CONFIG.frontend.cwd, 'package.json'),
      path.join(CONFIG.frontend.cwd, 'vite.config.js')
    ];

    for (const file of requiredFiles) {
      if (!fs.existsSync(file)) {
        throw new Error(`Required file not found: ${file}`);
      }
    }

    Logger.success('Environment validation passed', 'SETUP');
  }

  async setupEnvironment() {
    Logger.info('Setting up local environment...', 'SETUP');

    try {
      const setupProcess = await this.processManager.startProcess(
        'SETUP',
        'node',
        [path.join(__dirname, 'setup-local-env.js')],
        { stdio: 'inherit' }
      );

      await new Promise((resolve, reject) => {
        setupProcess.on('close', (code) => {
          if (code === 0) {
            Logger.success('Environment setup completed', 'SETUP');
            resolve();
          } else {
            reject(new Error(`Setup failed with code ${code}`));
          }
        });
      });
    } catch (error) {
      Logger.error(`Environment setup failed: ${error.message}`, 'SETUP');
      throw error;
    }
  }

  async startBackend() {
    Logger.info('Starting backend server...', 'BACKEND');

    await this.processManager.startProcess(
      'BACKEND',
      'node',
      [CONFIG.backend.script]
    );

    // Wait for backend to be ready
    await HealthChecker.waitForService(
      'Backend API',
      CONFIG.backend.port,
      CONFIG.backend.healthPath,
      CONFIG.timeouts.startup
    );
  }

  async startFrontend() {
    Logger.info('Starting frontend server...', 'FRONTEND');

    await this.processManager.startProcess(
      'FRONTEND',
      CONFIG.frontend.command,
      CONFIG.frontend.args,
      { cwd: CONFIG.frontend.cwd }
    );

    // Wait for frontend to be ready
    await HealthChecker.waitForService(
      'Frontend App',
      CONFIG.frontend.port,
      '/',
      CONFIG.timeouts.startup
    );
  }

  async start() {
    try {
      Logger.info('🚀 Starting Tunami MVP Development Environment', 'MAIN');
      Logger.info('', '');

      await this.validateEnvironment();
      await this.setupEnvironment();
      await this.startBackend();
      await this.startFrontend();

      Logger.info('', '');
      Logger.success('🎉 Development environment is ready!', 'MAIN');
      Logger.info('', '');
      Logger.info('📋 Access Points:', 'MAIN');
      Logger.info(`• Frontend: http://localhost:${CONFIG.frontend.port}`, 'MAIN');
      Logger.info(`• Backend API: http://localhost:${CONFIG.backend.port}`, 'MAIN');
      Logger.info('', '');
      Logger.info('💡 Test users available:', 'MAIN');
      Logger.info('• <EMAIL> / TestPass123!', 'MAIN');
      Logger.info('• <EMAIL> / TestPass123!', 'MAIN');
      Logger.info('• <EMAIL> / AdminPass123!', 'MAIN');
      Logger.info('', '');
      Logger.info('🔄 Servers are running... (Press Ctrl+C to stop)', 'MAIN');

      // Keep process alive
      return new Promise(() => {});

    } catch (error) {
      Logger.error(`Failed to start development environment: ${error.message}`, 'MAIN');
      await this.processManager.shutdown();
      process.exit(1);
    }
  }
}

// Start the development server
if (require.main === module) {
  const server = new DevelopmentServer();
  server.start().catch((error) => {
    Logger.error(`Startup error: ${error.message}`, 'MAIN');
    process.exit(1);
  });
}

module.exports = { DevelopmentServer, Logger, HealthChecker, ProcessManager };
