/**
 * Lambda function to create Stripe checkout session for subscriptions
 * Handles Tunami Supporter tier subscription creation
 */

const { successResponse, errorResponse } = require('../utils/responseHelper');

// Mock Stripe for local development
const mockStripe = {
  checkout: {
    sessions: {
      create: async (params) => {
        console.log('Mock Stripe checkout session creation:', params);
        return {
          id: 'cs_mock_' + Date.now(),
          url: 'https://checkout.stripe.com/mock-session-url',
          customer: 'cus_mock_customer',
          subscription: 'sub_mock_' + Date.now(),
          payment_status: 'unpaid',
          status: 'open'
        };
      }
    }
  }
};

/**
 * Main handler function
 */
exports.handler = async (event) => {
  console.log('Create checkout session request:', JSON.stringify(event, null, 2));

  try {
    // Extract user ID from Cognito authorizer
    const userId = event.requestContext?.authorizer?.claims?.sub || 'local-user';
    if (!userId) {
      return errorResponse('User not authenticated', 401, 'UNAUTHORIZED');
    }

    // Parse request body
    const body = JSON.parse(event.body || '{}');
    const { planId, successUrl, cancelUrl } = body;

    // Validate required fields
    if (!planId) {
      return errorResponse('Plan ID is required', 400, 'MISSING_PLAN_ID');
    }

    if (!successUrl || !cancelUrl) {
      return errorResponse('Success and cancel URLs are required', 400, 'MISSING_URLS');
    }

    // Validate plan ID (only supporting Tunami Supporter for MVP)
    if (planId !== 'tunami-supporter') {
      return errorResponse('Invalid plan ID', 400, 'INVALID_PLAN');
    }

    // Use mock Stripe for local development
    let stripe;
    if (process.env.NODE_ENV === 'development' || !process.env.STRIPE_SECRET_KEY || process.env.STRIPE_SECRET_KEY === 'sk_test_placeholder') {
      console.log('Using mock Stripe for local development');
      stripe = mockStripe;
    } else {
      stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
    }

    // Create checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      mode: 'subscription',
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: 'Tunami Supporter',
              description: 'Premium tier with exclusive features and creator support',
            },
            unit_amount: 999, // $9.99 in cents
            recurring: {
              interval: 'month',
            },
          },
          quantity: 1,
        },
      ],
      metadata: {
        userId: userId,
        planId: planId
      },
      subscription_data: {
        metadata: {
          userId: userId,
          planId: planId
        }
      },
      success_url: successUrl,
      cancel_url: cancelUrl,
      customer_email: event.requestContext?.authorizer?.claims?.email,
      allow_promotion_codes: true,
      billing_address_collection: 'auto',
      tax_id_collection: {
        enabled: true
      }
    });

    console.log('Checkout session created:', session.id);

    return successResponse({
      sessionId: session.id,
      checkoutUrl: session.url,
      customerId: session.customer,
      subscriptionId: session.subscription
    }, 'Checkout session created successfully');

  } catch (error) {
    console.error('Create checkout session error:', error);

    // Handle specific Stripe errors
    if (error.type === 'StripeCardError') {
      return errorResponse('Card error: ' + error.message, 400, 'CARD_ERROR');
    } else if (error.type === 'StripeRateLimitError') {
      return errorResponse('Rate limit exceeded', 429, 'RATE_LIMIT');
    } else if (error.type === 'StripeInvalidRequestError') {
      return errorResponse('Invalid request: ' + error.message, 400, 'INVALID_REQUEST');
    } else if (error.type === 'StripeAPIError') {
      return errorResponse('Stripe API error', 500, 'STRIPE_API_ERROR');
    } else if (error.type === 'StripeConnectionError') {
      return errorResponse('Network error', 500, 'NETWORK_ERROR');
    } else if (error.type === 'StripeAuthenticationError') {
      return errorResponse('Authentication error', 500, 'AUTH_ERROR');
    }

    return errorResponse('Failed to create checkout session', 500, 'CHECKOUT_ERROR');
  }
};
