#!/usr/bin/env node

/**
 * Simple Local Development Setup for Tunami MVP
 * This script starts the application without Docker dependencies
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkSamInstalled() {
  return new Promise((resolve) => {
    const sam = spawn('sam', ['--version'], { stdio: 'pipe' });
    sam.on('close', (code) => {
      resolve(code === 0);
    });
    sam.on('error', () => {
      resolve(false);
    });
  });
}

function buildSamApplication() {
  return new Promise((resolve, reject) => {
    log('🔨 Building SAM application...', 'cyan');
    
    const samBuild = spawn('sam', ['build'], {
      stdio: 'inherit',
      cwd: process.cwd()
    });

    samBuild.on('close', (code) => {
      if (code === 0) {
        log('✅ SAM application built successfully!', 'green');
        resolve();
      } else {
        reject(new Error(`SAM build failed with code ${code}`));
      }
    });
  });
}

function startBackend() {
  return new Promise((resolve, reject) => {
    log('🚀 Starting backend API server...', 'cyan');
    
    // Set environment variables for local development
    const env = {
      ...process.env,
      ENVIRONMENT: 'local',
      AWS_REGION: 'us-east-1',
      USER_POOL_ID: 'local_user_pool',
      USER_POOL_CLIENT_ID: 'local_client_id',
      USERS_TABLE_NAME: 'TunamiUsers-local',
      TRACKS_TABLE_NAME: 'TunamiTracks-local',
      LIKES_TABLE_NAME: 'TunamiLikes-local',
      COMMENTS_TABLE_NAME: 'TunamiComments-local',
      FOLLOWS_TABLE_NAME: 'TunamiFollows-local',
      AUDIO_BUCKET_NAME: 'tunami-audio-files-local',
      CLOUDFRONT_DOMAIN: '' // Empty for local development, will use direct S3 URLs
    };

    const samLocal = spawn('sam', [
      'local', 'start-api',
      '--port', '3001',
      '--host', '0.0.0.0'
    ], {
      stdio: 'inherit',
      cwd: process.cwd(),
      env: env
    });

    samLocal.on('close', (code) => {
      if (code === 0) {
        log('✅ Backend started successfully!', 'green');
        resolve();
      } else {
        reject(new Error(`SAM local failed with code ${code}`));
      }
    });

    // Give it a moment to start
    setTimeout(() => {
      log('✅ Backend should be starting on http://localhost:3001', 'green');
      resolve();
    }, 3000);
  });
}

function startFrontend() {
  return new Promise((resolve, reject) => {
    log('🎨 Starting frontend development server...', 'cyan');
    
    const viteProcess = spawn('npm', ['run', 'dev'], {
      stdio: 'inherit',
      cwd: path.join(process.cwd(), 'frontend')
    });

    viteProcess.on('close', (code) => {
      if (code === 0) {
        log('✅ Frontend started successfully!', 'green');
        resolve();
      } else {
        reject(new Error(`Frontend failed with code ${code}`));
      }
    });

    // Give it a moment to start
    setTimeout(() => {
      log('✅ Frontend should be starting on http://localhost:5173', 'green');
      resolve();
    }, 3000);
  });
}

async function main() {
  try {
    log('🚀 Starting Tunami MVP Simple Local Development', 'cyan');
    log('', 'reset');

    // Check SAM CLI
    const samInstalled = await checkSamInstalled();
    if (!samInstalled) {
      log('❌ AWS SAM CLI is not installed. Please install it first.', 'red');
      log('📖 Installation guide: https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html', 'yellow');
      process.exit(1);
    }

    // Build SAM application
    await buildSamApplication();

    log('', 'reset');
    log('🎉 Setup complete! Starting development servers...', 'green');
    log('', 'reset');
    log('📋 Access Points:', 'cyan');
    log('• Frontend: http://localhost:5173', 'yellow');
    log('• Backend API: http://localhost:3001', 'yellow');
    log('', 'reset');
    log('💡 Note: This simplified setup uses mock services for AWS Cognito', 'cyan');
    log('💡 For full local AWS services, install Docker and run: npm run start:local-services', 'cyan');
    log('', 'reset');
    log('🔄 Starting servers... (Press Ctrl+C to stop)', 'yellow');

    // Start both servers concurrently
    Promise.all([
      startBackend(),
      startFrontend()
    ]).catch((error) => {
      log(`❌ Error starting servers: ${error.message}`, 'red');
    });

  } catch (error) {
    log(`❌ Error: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  log('', 'reset');
  log('👋 Shutting down development servers...', 'yellow');
  process.exit(0);
});

main();
