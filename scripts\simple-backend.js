const express = require('express')
const cors = require('cors')

const app = express()
const PORT = 3001

// Middleware
app.use(cors())
app.use(express.json())

// Mock data
let reports = [
  {
    id: 'report-1',
    reason: 'inappropriate',
    status: 'pending',
    reportedAt: '2024-06-02T10:30:00Z',
    reporter: { username: 'user123', email: '<EMAIL>' },
    target: { 
      id: 'track-1', 
      title: 'AI Symphony No. 1', 
      artist: 'LocalUser',
      type: 'track'
    },
    description: 'This track contains inappropriate content',
    priority: 'medium'
  }
]

let stats = {
  totalReports: 23,
  pendingReports: 8,
  resolvedReports: 15,
  totalUsers: 1247,
  totalTracks: 3891,
  activeUsers: 342
}

// Routes
app.get('/health', (req, res) => {
  console.log('Health check requested')
  res.json({ status: 'healthy', message: 'Simple backend server running' })
})

app.get('/dev/admin/stats', (req, res) => {
  console.log('Stats requested')
  res.json(stats)
})

app.get('/dev/admin/reports', (req, res) => {
  console.log('Reports requested')
  res.json({
    reports: reports.filter(r => r.status === 'pending'),
    pagination: { hasMore: false, count: reports.length }
  })
})

app.post('/dev/reports/content', (req, res) => {
  console.log('Content report submitted:', req.body)
  const newReport = {
    id: 'report-' + Date.now(),
    ...req.body,
    status: 'pending',
    reportedAt: new Date().toISOString(),
    priority: 'medium'
  }
  reports.push(newReport)
  stats.totalReports++
  stats.pendingReports++
  res.status(201).json({ message: 'Report submitted', reportId: newReport.id })
})

app.post('/dev/admin/moderation/action', (req, res) => {
  console.log('Moderation action:', req.body)
  const { reportId, action } = req.body
  const report = reports.find(r => r.id === reportId)
  if (report) {
    report.status = 'resolved'
    report.action = action
    stats.pendingReports--
    stats.resolvedReports++
  }
  res.json({ message: `Action ${action} completed`, reportId, action })
})

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Simple Backend Server running on http://localhost:${PORT}`)
  console.log('📊 Available endpoints:')
  console.log('   GET  /health - Health check')
  console.log('   GET  /dev/admin/stats - Platform stats')
  console.log('   GET  /dev/admin/reports - Reports queue')
  console.log('   POST /dev/reports/content - Submit report')
  console.log('   POST /dev/admin/moderation/action - Take action')
  console.log('\n✅ Backend ready for testing!')
})
