const { DynamoDBClient, PutItemCommand } = require('@aws-sdk/client-dynamodb')
const { S3Client, HeadObjectCommand } = require('@aws-sdk/client-s3')
const { marshall } = require('@aws-sdk/util-dynamodb')
const { v4: uuidv4 } = require('uuid')
const { successResponse, errorResponse } = require('../utils/responseHelper')
const { validateInput } = require('../utils/validation')
const Joi = require('joi')

const dynamoClient = new DynamoDBClient({
  region: process.env.AWS_REGION,
  maxAttempts: 3,
  retryMode: 'adaptive'
})

const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  maxAttempts: 3,
  retryMode: 'adaptive'
})

// Enhanced validation schema for track metadata
const trackMetadataSchema = Joi.object({
  title: Joi.string().required().min(1).max(200).trim(),
  genre: Joi.string().required().min(1).max(50).trim(),
  description: Joi.string().allow('').max(1000).trim(),
  aiToolsUsed: Joi.array().items(Joi.string().max(100).trim()).max(10).default([]),
  audioFileUrl: Joi.string().required().uri(),
  fileKey: Joi.string().required().pattern(/^tracks\/[^\/]+\/\d{4}-\d{2}-\d{2}\/[^\/]+$/), // Validate file key format
  coverImageUrl: Joi.string().uri().allow('').default(''),
  isPublic: Joi.boolean().default(true),
  tags: Joi.array().items(Joi.string().max(50).trim()).max(20).default([]),
  duration: Joi.number().positive().max(600).optional(), // Duration in seconds, max 10 minutes
  fileSize: Joi.number().positive().max(50 * 1024 * 1024).optional() // File size in bytes
})

/**
 * Validates that the file exists in S3 and extracts metadata
 */
const validateAndExtractFileMetadata = async (fileKey) => {
  try {
    const headCommand = new HeadObjectCommand({
      Bucket: process.env.AUDIO_BUCKET_NAME,
      Key: fileKey
    })

    const response = await s3Client.send(headCommand)

    return {
      fileSize: response.ContentLength,
      contentType: response.ContentType,
      lastModified: response.LastModified,
      metadata: response.Metadata || {}
    }
  } catch (error) {
    if (error.name === 'NotFound') {
      throw new Error('Audio file not found in S3')
    }
    throw new Error(`Failed to validate audio file: ${error.message}`)
  }
}

/**
 * Sanitizes and processes tags and AI tools
 */
const processArrayFields = (array) => {
  if (!Array.isArray(array)) return []

  return array
    .filter(item => item && typeof item === 'string' && item.trim().length > 0)
    .map(item => item.trim().toLowerCase())
    .filter((item, index, arr) => arr.indexOf(item) === index) // Remove duplicates
    .slice(0, 20) // Limit to 20 items
}

/**
 * Lambda handler for creating track metadata in DynamoDB
 */
exports.handler = async (event) => {
  console.log('Create track metadata request:', JSON.stringify(event, null, 2))

  try {
    // Extract user ID from Cognito authorizer
    const userId = event.requestContext?.authorizer?.claims?.sub
    if (!userId) {
      console.error('Authentication failed: No user ID found')
      return errorResponse('User not authenticated', 401, 'UNAUTHORIZED')
    }

    // Parse and validate request body
    let body
    try {
      body = JSON.parse(event.body || '{}')
    } catch (parseError) {
      console.error('JSON parse error:', parseError)
      return errorResponse('Invalid JSON in request body', 400, 'INVALID_JSON')
    }

    const validation = validateInput(body, trackMetadataSchema)

    if (!validation.isValid) {
      console.error('Validation failed:', validation.errors)
      return errorResponse('Invalid input data', 400, 'VALIDATION_ERROR', validation.errors)
    }

    const trackData = validation.value

    // Validate that the file exists in S3 and extract metadata
    let fileMetadata
    try {
      fileMetadata = await validateAndExtractFileMetadata(trackData.fileKey)
    } catch (error) {
      console.error('File validation failed:', error)
      return errorResponse(error.message, 400, 'FILE_NOT_FOUND')
    }

    const trackId = uuidv4()
    const uploadDate = new Date().toISOString()

    // Process and sanitize array fields
    const processedAiTools = processArrayFields(trackData.aiToolsUsed)
    const processedTags = processArrayFields(trackData.tags)

    // Extract duration from S3 metadata if available, otherwise use provided value
    const duration = fileMetadata.metadata['duration-seconds']
      ? parseInt(fileMetadata.metadata['duration-seconds'])
      : trackData.duration || null

    // Prepare comprehensive track metadata for DynamoDB
    const trackMetadata = {
      trackId,
      creatorId: userId,
      title: trackData.title,
      genre: trackData.genre,
      description: trackData.description || '',
      aiToolsUsed: processedAiTools,
      audioFileUrl: trackData.audioFileUrl,
      fileKey: trackData.fileKey,
      coverImageUrl: trackData.coverImageUrl || '',
      uploadDate,
      isPublic: trackData.isPublic ? 'true' : 'false', // Store as string for GSI
      tags: processedTags,
      // File metadata
      fileSize: trackData.fileSize || fileMetadata.fileSize,
      contentType: fileMetadata.contentType,
      duration: duration,
      // Engagement metrics
      listenCount: 0,
      likeCount: 0,
      commentCount: 0,
      // Timestamps
      createdAt: uploadDate,
      updatedAt: uploadDate,
      // Additional metadata for search and discovery
      searchableText: `${trackData.title} ${trackData.genre} ${trackData.description} ${processedTags.join(' ')} ${processedAiTools.join(' ')}`.toLowerCase()
    }

    // Store in DynamoDB with enhanced error handling
    try {
      const putCommand = new PutItemCommand({
        TableName: process.env.TRACKS_TABLE_NAME,
        Item: marshall(trackMetadata),
        ConditionExpression: 'attribute_not_exists(trackId)' // Prevent duplicates
      })

      await dynamoClient.send(putCommand)
    } catch (dbError) {
      console.error('DynamoDB operation failed:', dbError)

      if (dbError.name === 'ConditionalCheckFailedException') {
        return errorResponse('Track with this ID already exists', 409, 'DUPLICATE_TRACK')
      }

      if (dbError.name === 'ResourceNotFoundException') {
        return errorResponse('Database table not found', 500, 'TABLE_NOT_FOUND')
      }

      throw dbError // Re-throw for general error handling
    }

    console.log(`Track metadata created successfully: ${trackId} by user ${userId}`)

    // Return comprehensive response with all relevant data
    return successResponse({
      track: {
        trackId,
        creatorId: userId,
        title: trackData.title,
        genre: trackData.genre,
        description: trackData.description,
        aiToolsUsed: processedAiTools,
        audioFileUrl: trackData.audioFileUrl,
        coverImageUrl: trackData.coverImageUrl,
        uploadDate,
        isPublic: trackData.isPublic,
        tags: processedTags,
        fileSize: trackMetadata.fileSize,
        duration: duration,
        listenCount: 0,
        likeCount: 0,
        commentCount: 0
      }
    }, 'Track metadata created successfully')

  } catch (error) {
    console.error('Create track metadata error:', error)

    // Enhanced error handling with specific error types
    if (error.name === 'ValidationError') {
      return errorResponse('Invalid input data', 400, 'VALIDATION_ERROR', error.details)
    }

    if (error.message.includes('not found in S3')) {
      return errorResponse('Audio file not found', 400, 'FILE_NOT_FOUND')
    }

    if (error.name === 'AccessDenied') {
      return errorResponse('Access denied to required resources', 403, 'ACCESS_DENIED')
    }

    if (error.name === 'ThrottlingException') {
      return errorResponse('Service temporarily unavailable', 503, 'SERVICE_THROTTLED')
    }

    return errorResponse('Failed to create track metadata', 500, 'METADATA_ERROR', {
      details: error.message
    })
  }
}
