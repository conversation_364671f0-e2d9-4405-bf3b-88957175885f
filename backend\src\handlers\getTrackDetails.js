const { DynamoDBClient, GetItemCommand, QueryCommand } = require('@aws-sdk/client-dynamodb')
const { unmarshall } = require('@aws-sdk/util-dynamodb')
const { successResponse, errorResponse } = require('../utils/responseHelper')

const dynamoClient = new DynamoDBClient({ region: process.env.AWS_REGION })

/**
 * Lambda handler for retrieving track details
 */
exports.handler = async (event) => {
  console.log('Get track details request:', JSON.stringify(event, null, 2))

  try {
    // Extract track ID from path parameters
    const trackId = event.pathParameters?.trackId
    if (!trackId) {
      return errorResponse('Track ID is required', 400, 'MISSING_TRACK_ID')
    }

    // Extract user ID from Cognito authorizer (optional for public tracks)
    const userId = event.requestContext?.authorizer?.claims?.sub

    // Get track from DynamoDB
    const getCommand = new GetItemCommand({
      TableName: process.env.TRACKS_TABLE_NAME,
      Key: {
        trackId: { S: trackId }
      }
    })

    const result = await dynamoClient.send(getCommand)

    if (!result.Item) {
      return errorResponse('Track not found', 404, 'TRACK_NOT_FOUND')
    }

    const track = unmarshall(result.Item)

    // Check privacy settings
    if (track.isPublic === 'false' && track.creatorId !== userId) {
      return errorResponse('Track not found', 404, 'TRACK_NOT_FOUND')
    }

    // Get creator information
    let creatorInfo = null
    if (track.creatorId) {
      try {
        const creatorQuery = new QueryCommand({
          TableName: process.env.USERS_TABLE_NAME,
          KeyConditionExpression: 'userId = :userId',
          ExpressionAttributeValues: {
            ':userId': { S: track.creatorId }
          },
          ProjectionExpression: 'userId, username, profileImageUrl, creatorStatus'
        })

        const creatorResult = await dynamoClient.send(creatorQuery)
        if (creatorResult.Items && creatorResult.Items.length > 0) {
          creatorInfo = unmarshall(creatorResult.Items[0])
        }
      } catch (error) {
        console.warn('Failed to fetch creator info:', error)
        // Continue without creator info
      }
    }

    // Format response
    const trackDetails = {
      trackId: track.trackId,
      title: track.title,
      genre: track.genre,
      description: track.description,
      aiToolsUsed: track.aiToolsUsed || [],
      audioFileUrl: track.audioFileUrl,
      coverImageUrl: track.coverImageUrl,
      uploadDate: track.uploadDate,
      isPublic: track.isPublic === 'true',
      tags: track.tags || [],
      listenCount: track.listenCount || 0,
      likeCount: track.likeCount || 0,
      commentCount: track.commentCount || 0,
      creator: creatorInfo ? {
        userId: creatorInfo.userId,
        username: creatorInfo.username,
        profileImageUrl: creatorInfo.profileImageUrl,
        creatorStatus: creatorInfo.creatorStatus
      } : null,
      isOwner: track.creatorId === userId
    }

    return successResponse(trackDetails, 'Track details retrieved successfully')

  } catch (error) {
    console.error('Get track details error:', error)
    return errorResponse('Failed to retrieve track details', 500, 'RETRIEVAL_ERROR')
  }
}
