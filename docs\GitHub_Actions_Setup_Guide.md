# GitHub Actions & CI/CD Setup Guide

This guide walks you through setting up the complete GitHub Actions CI/CD pipeline for the Tunami MVP project.

## 🎯 Overview

Our CI/CD pipeline provides:
- **Automated Testing** on every PR and push
- **Multi-Environment Deployment** (dev, staging, production)
- **Code Quality Checks** and coverage reporting
- **Security Scanning** and dependency checks
- **Automated Releases** with proper versioning

## 📋 Prerequisites

Before setting up GitHub Actions, ensure you have:

1. **GitHub Repository** with admin access
2. **AWS Account** with appropriate permissions
3. **AWS CLI** configured locally
4. **Domain Names** (optional) for custom URLs

## 🔧 Step 1: Repository Setup

### 1.1 Create GitHub Repository

1. Create a new repository on GitHub
2. Clone the repository locally
3. Copy all project files to the repository
4. Push the initial commit

```bash
git init
git add .
git commit -m "Initial commit: Tunami MVP Sprint 1 complete"
git branch -M main
git remote add origin https://github.com/your-username/tunami-mvp.git
git push -u origin main
```

### 1.2 Create Branch Structure

```bash
# Create develop branch for development work
git checkout -b develop
git push -u origin develop

# Create staging branch for staging deployments
git checkout -b staging
git push -u origin staging

# Return to main branch
git checkout main
```

### 1.3 Set Branch Protection Rules

In GitHub repository settings:

1. Go to **Settings** → **Branches**
2. Add protection rule for `main` branch:
   - ✅ Require a pull request before merging
   - ✅ Require status checks to pass before merging
   - ✅ Require branches to be up to date before merging
   - ✅ Include administrators
3. Add protection rule for `develop` branch:
   - ✅ Require status checks to pass before merging

## 🔐 Step 2: AWS Setup

### 2.1 Create IAM User for GitHub Actions

1. **Create IAM User:**
   ```bash
   aws iam create-user --user-name github-actions-tunami
   ```

2. **Create IAM Policy:**
   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Effect": "Allow",
         "Action": [
           "cloudformation:*",
           "lambda:*",
           "apigateway:*",
           "dynamodb:*",
           "cognito-idp:*",
           "s3:*",
           "cloudfront:*",
           "iam:*",
           "logs:*"
         ],
         "Resource": "*"
       }
     ]
   }
   ```

3. **Attach Policy and Create Access Keys:**
   ```bash
   aws iam put-user-policy --user-name github-actions-tunami --policy-name TunamiDeploymentPolicy --policy-document file://policy.json
   aws iam create-access-key --user-name github-actions-tunami
   ```

### 2.2 Create S3 Buckets for Frontend

```bash
# Development
aws s3 mb s3://tunami-frontend-dev --region us-east-1
aws s3 website s3://tunami-frontend-dev --index-document index.html

# Staging
aws s3 mb s3://tunami-frontend-staging --region us-east-1
aws s3 website s3://tunami-frontend-staging --index-document index.html

# Production
aws s3 mb s3://tunami-frontend-prod --region us-east-1
aws s3 website s3://tunami-frontend-prod --index-document index.html
```

### 2.3 Create CloudFront Distributions (Optional)

For each environment, create CloudFront distributions pointing to the S3 buckets for better performance and custom domains.

## 🔑 Step 3: GitHub Secrets Configuration

In your GitHub repository, go to **Settings** → **Secrets and variables** → **Actions** and add:

### 3.1 AWS Credentials
- `AWS_ACCESS_KEY_ID`: Your IAM user access key
- `AWS_SECRET_ACCESS_KEY`: Your IAM user secret key

### 3.2 Development Environment
- `DEV_S3_BUCKET`: `tunami-frontend-dev`
- `DEV_CLOUDFRONT_DISTRIBUTION_ID`: Your CloudFront distribution ID (if using)
- `DEV_CLOUDFRONT_DOMAIN`: Your CloudFront domain (if using)

### 3.3 Staging Environment
- `STAGING_S3_BUCKET`: `tunami-frontend-staging`
- `STAGING_CLOUDFRONT_DISTRIBUTION_ID`: Your CloudFront distribution ID (if using)
- `STAGING_CLOUDFRONT_DOMAIN`: Your CloudFront domain (if using)

### 3.4 Production Environment
- `PROD_S3_BUCKET`: `tunami-frontend-prod`
- `PROD_CLOUDFRONT_DISTRIBUTION_ID`: Your CloudFront distribution ID (if using)
- `PROD_CLOUDFRONT_DOMAIN`: Your CloudFront domain (if using)

## 🌍 Step 4: Environment Configuration

### 4.1 Create GitHub Environments

1. Go to **Settings** → **Environments**
2. Create three environments:
   - `development` (no protection rules)
   - `staging` (require reviewers)
   - `production` (require reviewers + deployment branches: main only)

### 4.2 Environment-Specific Secrets

For each environment, you can override secrets if needed:
- Different AWS accounts
- Different domain names
- Different configuration values

## 🚀 Step 5: Workflow Testing

### 5.1 Test Development Deployment

1. **Create a feature branch:**
   ```bash
   git checkout develop
   git checkout -b feature/test-ci-cd
   ```

2. **Make a small change and push:**
   ```bash
   echo "# CI/CD Test" >> test.md
   git add test.md
   git commit -m "test: CI/CD pipeline setup"
   git push origin feature/test-ci-cd
   ```

3. **Create Pull Request** to `develop` branch
4. **Verify** that tests run automatically
5. **Merge** the PR to trigger development deployment

### 5.2 Test Staging Deployment

1. **Merge develop to staging:**
   ```bash
   git checkout staging
   git merge develop
   git push origin staging
   ```

2. **Verify** staging deployment with manual approval

### 5.3 Test Production Deployment

1. **Merge staging to main:**
   ```bash
   git checkout main
   git merge staging
   git push origin main
   ```

2. **Verify** production deployment with manual approval

## 📊 Step 6: Monitoring and Notifications

### 6.1 Slack/Discord Notifications (Optional)

Add webhook URLs to GitHub secrets:
- `SLACK_WEBHOOK_URL`
- `DISCORD_WEBHOOK_URL`

### 6.2 Email Notifications

Configure in GitHub repository settings under **Notifications**.

## 🔍 Step 7: Troubleshooting

### Common Issues:

1. **AWS Permissions Error:**
   - Verify IAM user has correct permissions
   - Check AWS credentials in GitHub secrets

2. **S3 Bucket Access Denied:**
   - Ensure bucket exists and is in correct region
   - Verify bucket policy allows GitHub Actions user

3. **CloudFormation Stack Errors:**
   - Check AWS CloudFormation console for detailed errors
   - Verify SAM template syntax

4. **Frontend Build Failures:**
   - Check Node.js version compatibility
   - Verify all dependencies are listed in package.json

### Debug Commands:

```bash
# Test SAM template locally
sam validate

# Test deployment locally
sam build
sam local start-api

# Check AWS credentials
aws sts get-caller-identity

# Test S3 access
aws s3 ls s3://your-bucket-name
```

## ✅ Verification Checklist

- [ ] Repository created with proper branch structure
- [ ] Branch protection rules configured
- [ ] AWS IAM user created with correct permissions
- [ ] S3 buckets created for all environments
- [ ] GitHub secrets configured
- [ ] GitHub environments set up with protection rules
- [ ] CI/CD pipeline runs successfully
- [ ] Development auto-deployment works
- [ ] Staging manual approval works
- [ ] Production deployment works
- [ ] All tests pass in pipeline
- [ ] Code coverage reports generated

## 🎉 Success!

Once all steps are completed, you'll have a fully automated CI/CD pipeline that:

- ✅ Runs tests on every PR
- ✅ Automatically deploys to development
- ✅ Requires approval for staging/production
- ✅ Provides deployment status updates
- ✅ Maintains code quality standards
- ✅ Tracks deployment history

Your team can now focus on development while the pipeline handles testing, building, and deployment automatically!
