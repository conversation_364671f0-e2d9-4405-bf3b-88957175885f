# Pull Request

## 📋 Description

Brief description of the changes in this PR.

## 🎯 Type of Change

- [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
- [ ] ✨ New feature (non-breaking change which adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🔧 Configuration change
- [ ] 🧪 Test update
- [ ] ♻️ Code refactoring

## 🚀 Sprint & Epic

- **Sprint:** [e.g., Sprint 2]
- **Epic:** [e.g., Epic 2: AI Music Content Management]
- **Related Tasks:** [e.g., Task 2.1, Task 2.2]

## 🧪 Testing

### Test Coverage
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] End-to-end tests added/updated
- [ ] Manual testing completed

### Test Results
- [ ] All existing tests pass
- [ ] New tests pass
- [ ] Code coverage maintained/improved

## 📝 Checklist

### Code Quality
- [ ] Code follows project style guidelines
- [ ] Self-review of code completed
- [ ] Code is properly commented
- [ ] No console.log statements left in production code
- [ ] Error handling implemented appropriately

### Documentation
- [ ] README updated (if applicable)
- [ ] API documentation updated (if applicable)
- [ ] Inline code documentation added
- [ ] Configuration changes documented

### Security & Performance
- [ ] Security considerations reviewed
- [ ] Performance impact assessed
- [ ] No sensitive data exposed
- [ ] Input validation implemented

### AWS & Infrastructure
- [ ] SAM template updated (if applicable)
- [ ] Environment variables documented
- [ ] IAM permissions reviewed
- [ ] Resource naming follows conventions

## 🔗 Related Issues

Closes #[issue_number]
Related to #[issue_number]

## 📸 Screenshots (if applicable)

Add screenshots or GIFs to demonstrate the changes.

## 🚨 Breaking Changes

List any breaking changes and migration steps required.

## 📋 Deployment Notes

Any special deployment considerations or steps required.

## 🧑‍💻 Reviewer Notes

Any specific areas you'd like reviewers to focus on or questions you have.

---

**Definition of Done Checklist:**
- [ ] All acceptance criteria met
- [ ] Code reviewed and approved
- [ ] Tests passing in CI/CD
- [ ] Documentation updated
- [ ] Ready for deployment
