/**
 * Mock Cognito Service for Local Development
 * This service simulates AWS Cognito functionality for local development
 */

const crypto = require('crypto');

class MockCognitoService {
  constructor() {
    // In-memory storage for local development
    this.users = new Map();
    this.tokens = new Map();
  }

  /**
   * Creates a new user (mock implementation)
   * @param {string} username - Username
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise<Object>} - Created user information
   */
  async createUser(username, email, password) {
    try {
      // Check if user already exists
      if (this.users.has(email)) {
        throw new Error('User with this email already exists');
      }

      // Create user ID
      const userId = crypto.randomUUID();

      // Store user (in production, password would be hashed)
      const user = {
        userId,
        username,
        email,
        password, // In real implementation, this would be hashed
        createdAt: new Date().toISOString(),
        emailVerified: true
      };

      this.users.set(email, user);

      console.log(`[MOCK COGNITO] Created user: ${email}`);

      return {
        userId,
        email,
        username
      };
    } catch (error) {
      console.error('Error creating user in Mock Cognito:', error);
      throw error;
    }
  }

  /**
   * Authenticates a user and returns mock tokens
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise<Object>} - Authentication tokens and user info
   */
  async authenticateUser(email, password) {
    try {
      const user = this.users.get(email);

      if (!user) {
        throw new Error('User not found');
      }

      if (user.password !== password) {
        throw new Error('Invalid email or password');
      }

      // Generate mock tokens
      const accessToken = this.generateMockToken(user, 'access');
      const idToken = this.generateMockToken(user, 'id');
      const refreshToken = this.generateMockToken(user, 'refresh');

      // Store tokens for validation
      this.tokens.set(accessToken, user);

      console.log(`[MOCK COGNITO] Authenticated user: ${email}`);

      return {
        accessToken,
        idToken,
        refreshToken,
        expiresIn: 3600, // 1 hour
        user: {
          userId: user.userId,
          email: user.email,
          username: user.username
        }
      };
    } catch (error) {
      console.error('Error authenticating user:', error);
      throw error;
    }
  }

  /**
   * Gets user information from mock token
   * @param {string} accessToken - User's access token
   * @returns {Promise<Object>} - User information
   */
  async getUserFromToken(accessToken) {
    try {
      const user = this.tokens.get(accessToken);

      if (!user) {
        throw new Error('Invalid or expired token');
      }

      console.log(`[MOCK COGNITO] Retrieved user from token: ${user.email}`);

      return {
        userId: user.userId,
        email: user.email,
        username: user.username
      };
    } catch (error) {
      console.error('Error getting user from token:', error);
      throw new Error('Invalid or expired token');
    }
  }

  /**
   * Generates a mock JWT-like token
   * @param {Object} user - User object
   * @param {string} type - Token type (access, id, refresh)
   * @returns {string} - Mock token
   */
  generateMockToken(user, type) {
    const header = {
      alg: 'HS256',
      typ: 'JWT'
    };

    const payload = {
      sub: user.userId,
      username: user.username,
      email: user.email,
      token_use: type,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600 // 1 hour
    };

    // Create a simple mock token (not cryptographically secure)
    const headerEncoded = Buffer.from(JSON.stringify(header)).toString('base64');
    const payloadEncoded = Buffer.from(JSON.stringify(payload)).toString('base64');
    const signature = crypto.createHash('sha256')
      .update(`${headerEncoded}.${payloadEncoded}.mock-secret`)
      .digest('base64');

    return `${headerEncoded}.${payloadEncoded}.${signature}`;
  }

  /**
   * Seed some test users for development
   */
  seedTestUsers() {
    const testUsers = [
      {
        username: 'testuser1',
        email: '<EMAIL>',
        password: 'TestPass123!'
      },
      {
        username: 'testuser2',
        email: '<EMAIL>',
        password: 'TestPass123!'
      },
      {
        username: 'admin',
        email: '<EMAIL>',
        password: 'AdminPass123!'
      }
    ];

    testUsers.forEach(async (userData) => {
      try {
        await this.createUser(userData.username, userData.email, userData.password);
      } catch (error) {
        // User might already exist, ignore error
      }
    });

    console.log('[MOCK COGNITO] Seeded test users');
  }
}

module.exports = MockCognitoService;
