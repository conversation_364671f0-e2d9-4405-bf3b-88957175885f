<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tunami Admin Panel - Live Backend Integration</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .loading { animation: spin 1s linear infinite; }
        @keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold">🎵</span>
                    </div>
                    <h1 class="text-2xl font-bold text-gray-900">Tunami Admin Panel</h1>
                    <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">LIVE</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div id="connectionStatus" class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-yellow-500 rounded-full loading"></div>
                        <span class="text-sm text-gray-700">Connecting...</span>
                    </div>
                    <span class="text-sm text-gray-700">Admin: <EMAIL></span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Backend Status -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">🚀 Live Backend Integration Status</h2>
            <div id="backendStatus" class="space-y-2">
                <div class="text-sm text-gray-600">Checking backend connection...</div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="border-b border-gray-200 mb-8">
            <nav class="-mb-px flex space-x-8">
                <button onclick="showTab('overview')" id="tab-overview" class="tab-button border-b-2 border-blue-500 text-blue-600 py-2 px-1 font-medium text-sm">
                    📊 Overview
                </button>
                <button onclick="showTab('reports')" id="tab-reports" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 py-2 px-1 font-medium text-sm">
                    🚨 Reports Queue
                </button>
                <button onclick="showTab('tracks')" id="tab-tracks" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 py-2 px-1 font-medium text-sm">
                    🎵 Tracks
                </button>
                <button onclick="showTab('test')" id="tab-test" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 py-2 px-1 font-medium text-sm">
                    🧪 API Tests
                </button>
            </nav>
        </div>

        <!-- Tab Content -->
        <div id="content-overview" class="tab-content active">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">Platform Overview</h2>
            <div id="statsGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="bg-white rounded-lg shadow p-6 animate-pulse">
                    <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div class="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
            </div>
        </div>

        <div id="content-reports" class="tab-content">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-900">Reports Queue</h2>
                <button onclick="refreshReports()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    🔄 Refresh
                </button>
            </div>
            <div id="reportsContainer">
                <div class="text-center py-8">
                    <div class="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                    <p class="text-gray-500">Loading reports...</p>
                </div>
            </div>
        </div>

        <div id="content-tracks" class="tab-content">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">Platform Tracks</h2>
            <div id="tracksContainer">
                <div class="text-center py-8">
                    <div class="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                    <p class="text-gray-500">Loading tracks...</p>
                </div>
            </div>
        </div>

        <div id="content-test" class="tab-content">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">API Testing</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">🚨 Test Report Submission</h3>
                    <button onclick="testReportSubmission()" class="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                        Submit Test Report
                    </button>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">⚖️ Test Moderation Action</h3>
                    <button onclick="testModerationAction()" class="w-full px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700">
                        Test Moderation
                    </button>
                </div>
            </div>
            <div id="testResults" class="mt-6 bg-gray-100 rounded-lg p-4 hidden">
                <h4 class="font-medium text-gray-900 mb-2">Test Results:</h4>
                <pre id="testOutput" class="text-sm text-gray-700 whitespace-pre-wrap"></pre>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001';
        let currentUser = null;
        let backendConnected = false;

        // Tab management
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('border-blue-500', 'text-blue-600');
                btn.classList.add('border-transparent', 'text-gray-500');
            });

            // Show selected tab
            document.getElementById(`content-${tabName}`).classList.add('active');
            document.getElementById(`tab-${tabName}`).classList.remove('border-transparent', 'text-gray-500');
            document.getElementById(`tab-${tabName}`).classList.add('border-blue-500', 'text-blue-600');

            // Load content
            if (tabName === 'overview') loadStats();
            if (tabName === 'reports') loadReports();
            if (tabName === 'tracks') loadTracks();
        }

        // Check backend connection
        async function checkBackendConnection() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    backendConnected = true;
                    document.getElementById('connectionStatus').innerHTML = `
                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span class="text-sm text-gray-700">Connected</span>
                    `;
                    
                    document.getElementById('backendStatus').innerHTML = `
                        <div class="flex items-center space-x-2 mb-2">
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-sm font-medium text-green-700">Backend: Connected</span>
                        </div>
                        <div class="text-sm text-gray-600">
                            <strong>Version:</strong> ${data.version}<br>
                            <strong>Features:</strong> ${data.features.join(', ')}<br>
                            <strong>Timestamp:</strong> ${new Date(data.timestamp).toLocaleString()}
                        </div>
                    `;
                    
                    // Load initial data
                    loadStats();
                    return true;
                }
            } catch (error) {
                console.error('Backend connection failed:', error);
            }
            
            backendConnected = false;
            document.getElementById('connectionStatus').innerHTML = `
                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                <span class="text-sm text-gray-700">Disconnected</span>
            `;
            
            document.getElementById('backendStatus').innerHTML = `
                <div class="flex items-center space-x-2 mb-2">
                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                    <span class="text-sm font-medium text-red-700">Backend: Not Connected</span>
                </div>
                <div class="text-sm text-gray-600">
                    Please ensure the backend server is running on localhost:3001
                </div>
            `;
            
            return false;
        }

        // Load platform statistics
        async function loadStats() {
            if (!backendConnected) return;
            
            try {
                const response = await fetch(`${API_BASE}/dev/admin/stats`);
                const result = await response.json();
                
                if (result.success) {
                    const stats = result.data;
                    document.getElementById('statsGrid').innerHTML = `
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-red-100">
                                    <span class="text-2xl">🚨</span>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Pending Reports</p>
                                    <p class="text-2xl font-semibold text-gray-900">${stats.pendingReports}</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-green-100">
                                    <span class="text-2xl">✅</span>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Resolved Reports</p>
                                    <p class="text-2xl font-semibold text-gray-900">${stats.resolvedReports}</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-blue-100">
                                    <span class="text-2xl">👥</span>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Total Users</p>
                                    <p class="text-2xl font-semibold text-gray-900">${stats.totalUsers}</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-purple-100">
                                    <span class="text-2xl">🎵</span>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Total Tracks</p>
                                    <p class="text-2xl font-semibold text-gray-900">${stats.totalTracks}</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-indigo-100">
                                    <span class="text-2xl">📈</span>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Active Users</p>
                                    <p class="text-2xl font-semibold text-gray-900">${stats.activeUsers}</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-gray-100">
                                    <span class="text-2xl">📊</span>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Total Reports</p>
                                    <p class="text-2xl font-semibold text-gray-900">${stats.totalReports}</p>
                                </div>
                            </div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        // Load reports
        async function loadReports() {
            if (!backendConnected) return;
            
            try {
                const response = await fetch(`${API_BASE}/dev/admin/reports?status=pending`);
                const result = await response.json();
                
                if (result.success) {
                    const reports = result.data.reports;
                    
                    if (reports.length === 0) {
                        document.getElementById('reportsContainer').innerHTML = `
                            <div class="text-center py-8">
                                <div class="text-6xl mb-4">✅</div>
                                <p class="text-gray-500">No pending reports</p>
                            </div>
                        `;
                        return;
                    }
                    
                    document.getElementById('reportsContainer').innerHTML = reports.map(report => `
                        <div class="bg-white rounded-lg shadow p-6 mb-4">
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex items-center space-x-3">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full ${
                                        report.priority === 'high' ? 'bg-red-100 text-red-800' :
                                        report.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                        'bg-green-100 text-green-800'
                                    }">
                                        ${report.priority} priority
                                    </span>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                                        ${report.status}
                                    </span>
                                </div>
                                <span class="text-sm text-gray-500">${new Date(report.reportedAt).toLocaleDateString()}</span>
                            </div>
                            <h3 class="font-medium text-gray-900 mb-2">
                                ${report.target.title || report.target.username} (${report.contentType})
                            </h3>
                            <p class="text-sm text-gray-600 mb-2"><strong>Reason:</strong> ${report.reason}</p>
                            <p class="text-sm text-gray-600 mb-4">${report.description}</p>
                            <div class="flex space-x-2">
                                <button onclick="takeAction('${report.reportId}', 'dismiss')" class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">
                                    Dismiss
                                </button>
                                <button onclick="takeAction('${report.reportId}', 'warn')" class="px-3 py-2 text-sm bg-yellow-100 text-yellow-700 rounded-md hover:bg-yellow-200">
                                    Warn User
                                </button>
                                <button onclick="takeAction('${report.reportId}', 'remove')" class="px-3 py-2 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200">
                                    Remove Content
                                </button>
                            </div>
                        </div>
                    `).join('');
                }
            } catch (error) {
                console.error('Error loading reports:', error);
            }
        }

        // Load tracks
        async function loadTracks() {
            if (!backendConnected) return;
            
            try {
                const response = await fetch(`${API_BASE}/dev/tracks`);
                const result = await response.json();
                
                if (result.success) {
                    const tracks = result.data.tracks;
                    document.getElementById('tracksContainer').innerHTML = tracks.map(track => `
                        <div class="bg-white rounded-lg shadow p-6 mb-4">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="font-medium text-gray-900 mb-2">${track.title}</h3>
                                    <p class="text-sm text-gray-600 mb-2">by ${track.creator.username}</p>
                                    <p class="text-sm text-gray-500 mb-2">${track.description}</p>
                                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                                        <span>❤️ ${track.likeCount} likes</span>
                                        <span>💬 ${track.commentCount} comments</span>
                                        <span>▶️ ${track.listenCount} plays</span>
                                    </div>
                                </div>
                                <button onclick="reportTrack('${track.trackId}')" class="px-3 py-2 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200">
                                    🚩 Report
                                </button>
                            </div>
                        </div>
                    `).join('');
                }
            } catch (error) {
                console.error('Error loading tracks:', error);
            }
        }

        // Take moderation action
        async function takeAction(reportId, action) {
            if (!backendConnected) return;
            
            try {
                const response = await fetch(`${API_BASE}/dev/admin/moderation/action`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ reportId, action })
                });
                
                const result = await response.json();
                if (result.success) {
                    alert(`✅ ${result.data.message}`);
                    loadReports(); // Refresh reports
                    loadStats(); // Refresh stats
                } else {
                    alert(`❌ Error: ${result.error.message}`);
                }
            } catch (error) {
                console.error('Error taking action:', error);
                alert('❌ Failed to take action');
            }
        }

        // Report track
        async function reportTrack(trackId) {
            if (!backendConnected) return;
            
            const reason = prompt('Report reason:', 'inappropriate');
            if (!reason) return;
            
            try {
                const response = await fetch(`${API_BASE}/dev/reports/content`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        contentId: trackId,
                        contentType: 'track',
                        reason,
                        description: 'Test report from admin panel'
                    })
                });
                
                const result = await response.json();
                if (result.success) {
                    alert(`✅ ${result.data.message}`);
                    loadStats(); // Refresh stats
                } else {
                    alert(`❌ Error: ${result.error.message}`);
                }
            } catch (error) {
                console.error('Error reporting track:', error);
                alert('❌ Failed to submit report');
            }
        }

        // Test functions
        async function testReportSubmission() {
            const result = await reportTrack('track-1');
            showTestResult('Report Submission Test', result);
        }

        async function testModerationAction() {
            // First get a report to act on
            try {
                const response = await fetch(`${API_BASE}/dev/admin/reports?status=pending`);
                const result = await response.json();
                
                if (result.success && result.data.reports.length > 0) {
                    const reportId = result.data.reports[0].reportId;
                    await takeAction(reportId, 'dismiss');
                    showTestResult('Moderation Action Test', 'Action completed successfully');
                } else {
                    showTestResult('Moderation Action Test', 'No pending reports to test with');
                }
            } catch (error) {
                showTestResult('Moderation Action Test', `Error: ${error.message}`);
            }
        }

        function showTestResult(testName, result) {
            document.getElementById('testResults').classList.remove('hidden');
            document.getElementById('testOutput').textContent = `${testName}:\n${JSON.stringify(result, null, 2)}`;
        }

        function refreshReports() {
            loadReports();
        }

        // Initialize
        window.onload = async function() {
            await checkBackendConnection();
            showTab('overview');
        };
    </script>
</body>
</html>
