{"environment": "staging", "aws": {"region": "us-east-1", "stackName": "tunami-mvp-staging"}, "cognito": {"userPoolName": "TunamiUserPool-staging", "userPoolClientName": "TunamiUserPoolClient-staging"}, "dynamodb": {"usersTableName": "TunamiUsers-staging", "tracksTableName": "TunamiTracks-staging", "likesTableName": "TunamiLikes-staging", "commentsTableName": "TunamiComments-staging", "followsTableName": "TunamiFollows-staging", "reportsTableName": "TunamiReports-staging", "subscriptionsTableName": "TunamiSubscriptions-staging", "transactionsTableName": "TunamiTransactions-staging"}, "s3": {"audioFilesBucket": "tunami-audio-files-staging", "staticWebsiteBucket": "tunami-frontend-staging"}, "cloudfront": {"distributionComment": "Tunami MVP Staging Distribution"}, "api": {"stageName": "staging", "throttling": {"burstLimit": 200, "rateLimit": 100}}, "monitoring": {"logRetentionDays": 14, "enableXRay": true}}