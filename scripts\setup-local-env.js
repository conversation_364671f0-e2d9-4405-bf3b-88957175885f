#!/usr/bin/env node

/**
 * Local Environment Setup Script for Tunami MVP
 * This script sets up the local development environment by:
 * 1. Creating local environment files
 * 2. Setting up local AWS service configurations
 * 3. Creating mock data for development
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function createDirectoryIfNotExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    log(`✅ Created directory: ${dirPath}`, 'green');
  }
}

function createFileIfNotExists(filePath, content) {
  if (!fs.existsSync(filePath)) {
    fs.writeFileSync(filePath, content);
    log(`✅ Created file: ${filePath}`, 'green');
  } else {
    log(`ℹ️  File already exists: ${filePath}`, 'yellow');
  }
}

function setupLocalEnvironment() {
  log('🚀 Setting up Tunami MVP Local Development Environment', 'cyan');
  log('', 'reset');

  // Create local environment directory
  const localEnvDir = path.join(__dirname, '..', 'environments', 'local');
  createDirectoryIfNotExists(localEnvDir);

  // Create local configuration
  const localConfig = {
    environment: 'local',
    aws: {
      region: 'us-east-1',
      stackName: 'tunami-mvp-local',
      endpoint: 'http://localhost:4566' // LocalStack endpoint
    },
    api: {
      baseUrl: 'http://localhost:3001',
      stageName: 'local'
    },
    cognito: {
      userPoolId: 'local_user_pool',
      userPoolClientId: 'local_client_id',
      region: 'us-east-1'
    },
    dynamodb: {
      endpoint: 'http://localhost:8000',
      region: 'us-east-1',
      usersTableName: 'TunamiUsers-local',
      tracksTableName: 'TunamiTracks-local',
      likesTableName: 'TunamiLikes-local',
      commentsTableName: 'TunamiComments-local',
      followsTableName: 'TunamiFollows-local'
    },
    s3: {
      endpoint: 'http://localhost:4566',
      region: 'us-east-1',
      audioFilesBucket: 'tunami-audio-files-local',
      staticWebsiteBucket: 'tunami-frontend-local'
    }
  };

  const localConfigPath = path.join(localEnvDir, 'config.json');
  createFileIfNotExists(localConfigPath, JSON.stringify(localConfig, null, 2));

  // Create frontend .env.local file
  const frontendEnvContent = `# Tunami MVP Local Development Environment
VITE_API_BASE_URL=http://localhost:3001
VITE_ENVIRONMENT=local
VITE_AWS_REGION=us-east-1
VITE_COGNITO_USER_POOL_ID=local_user_pool
VITE_COGNITO_USER_POOL_CLIENT_ID=local_client_id
`;

  const frontendEnvPath = path.join(__dirname, '..', 'frontend', '.env.local');
  createFileIfNotExists(frontendEnvPath, frontendEnvContent);

  // Create backend .env.local file
  const backendEnvContent = `# Tunami MVP Backend Local Development Environment
ENVIRONMENT=local
AWS_REGION=us-east-1
USER_POOL_ID=local_user_pool
USER_POOL_CLIENT_ID=local_client_id
USERS_TABLE_NAME=TunamiUsers-local
TRACKS_TABLE_NAME=TunamiTracks-local
LIKES_TABLE_NAME=TunamiLikes-local
COMMENTS_TABLE_NAME=TunamiComments-local
FOLLOWS_TABLE_NAME=TunamiFollows-local
AUDIO_BUCKET_NAME=tunami-audio-files-local
DYNAMODB_ENDPOINT=http://localhost:8000
S3_ENDPOINT=http://localhost:4566
`;

  const backendEnvPath = path.join(__dirname, '..', 'backend', '.env.local');
  createFileIfNotExists(backendEnvPath, backendEnvContent);

  log('', 'reset');
  log('✅ Local environment setup completed!', 'green');
  log('', 'reset');
  log('📋 Next steps:', 'cyan');
  log('1. Install Docker Desktop (if not already installed)', 'yellow');
  log('2. Run: npm run dev:setup', 'yellow');
  log('3. Open http://localhost:5173 for frontend', 'yellow');
  log('4. Backend API will be available at http://localhost:3001', 'yellow');
  log('', 'reset');
}

// Run the setup
try {
  setupLocalEnvironment();
} catch (error) {
  log(`❌ Error setting up local environment: ${error.message}`, 'red');
  process.exit(1);
}
