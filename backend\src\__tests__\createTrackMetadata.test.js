// Mock AWS SDK before importing handler
jest.mock('@aws-sdk/client-dynamodb', () => ({
  DynamoDBClient: jest.fn().mockImplementation(() => ({
    send: jest.fn()
  })),
  PutItemCommand: jest.fn()
}))

jest.mock('@aws-sdk/client-s3', () => ({
  S3Client: jest.fn().mockImplementation(() => ({
    send: jest.fn()
  })),
  HeadObjectCommand: jest.fn()
}))

jest.mock('@aws-sdk/util-dynamodb', () => ({
  marshall: jest.fn()
}))

const { handler } = require('../handlers/createTrackMetadata')
const { DynamoDBClient, PutItemCommand } = require('@aws-sdk/client-dynamodb')
const { S3Client, HeadObjectCommand } = require('@aws-sdk/client-s3')
const { marshall } = require('@aws-sdk/util-dynamodb')

describe('createTrackMetadata Lambda Handler', () => {
  let mockDynamoSend
  let mockS3Send
  let mockDynamoClient
  let mockS3Client

  beforeEach(() => {
    jest.clearAllMocks()

    // Mock environment variables
    process.env.TRACKS_TABLE_NAME = 'test-tracks-table'
    process.env.AUDIO_BUCKET_NAME = 'test-audio-bucket'
    process.env.AWS_REGION = 'us-east-1'

    // Mock DynamoDB client
    mockDynamoSend = jest.fn()
    mockDynamoClient = {
      send: mockDynamoSend
    }
    DynamoDBClient.mockImplementation(() => mockDynamoClient)

    // Mock S3 client
    mockS3Send = jest.fn()
    mockS3Client = {
      send: mockS3Send
    }
    S3Client.mockImplementation(() => mockS3Client)

    // Mock marshall function
    marshall.mockImplementation((obj) => obj)

    // Default S3 HeadObject response (file exists)
    mockS3Send.mockImplementation(() => Promise.resolve({
      ContentLength: 5000000,
      ContentType: 'audio/mpeg',
      LastModified: new Date(),
      Metadata: {
        'duration-seconds': '180'
      }
    }))
  })

  const createMockEvent = (body, userId = 'test-user-123') => ({
    body: JSON.stringify(body),
    requestContext: {
      authorizer: {
        claims: {
          sub: userId
        }
      }
    }
  })

  describe('Success Cases', () => {
    test('should create track metadata successfully', async () => {
      mockDynamoSend.mockResolvedValue({})

      const event = createMockEvent({
        title: 'Test Track',
        genre: 'Electronic',
        description: 'A test track',
        aiToolsUsed: ['AIVA', 'Soundraw'],
        audioFileUrl: 'https://test-bucket.s3.amazonaws.com/track.mp3',
        fileKey: 'tracks/test-user-123/2024-01-15/track.mp3',
        isPublic: true,
        tags: ['ambient', 'chill']
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
      expect(response.data.track).toHaveProperty('trackId')
      expect(response.data.track).toHaveProperty('creatorId', 'test-user-123')
      expect(response.data.track).toHaveProperty('title', 'Test Track')
      expect(response.data.track).toHaveProperty('genre', 'Electronic')
      expect(response.data.track).toHaveProperty('duration', 180)
      expect(response.data.track).toHaveProperty('fileSize', 5000000)

      // Verify S3 HeadObject was called to validate file
      expect(mockS3Send).toHaveBeenCalledWith(expect.any(Object))

      // Verify DynamoDB was called
      expect(mockDynamoSend).toHaveBeenCalledWith(expect.any(Object))
    })

    test('should handle minimal required fields', async () => {
      mockDynamoSend.mockResolvedValue({})

      const event = createMockEvent({
        title: 'Minimal Track',
        genre: 'Jazz',
        audioFileUrl: 'https://test-bucket.s3.amazonaws.com/minimal.mp3',
        fileKey: 'tracks/test-user-123/2024-01-15/minimal.mp3'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
      expect(response.data.track.title).toBe('Minimal Track')
      expect(response.data.track.genre).toBe('Jazz')
    })

    test('should default isPublic to true when not specified', async () => {
      mockDynamoSend.mockResolvedValue({})

      const event = createMockEvent({
        title: 'Public Track',
        genre: 'Pop',
        audioFileUrl: 'https://test-bucket.s3.amazonaws.com/public.mp3',
        fileKey: 'tracks/test-user-123/2024-01-15/public.mp3'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.data.track.isPublic).toBe(true)
    })

    test('should handle file not found in S3', async () => {
      const notFoundError = new Error('File not found')
      notFoundError.name = 'NotFound'

      // Override the default mock for this specific test
      mockS3Send.mockImplementationOnce(() => Promise.reject(notFoundError))

      const event = createMockEvent({
        title: 'Missing File Track',
        genre: 'Electronic',
        audioFileUrl: 'https://test-bucket.s3.amazonaws.com/missing.mp3',
        fileKey: 'tracks/test-user-123/2024-01-15/missing.mp3'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('FILE_NOT_FOUND')
    })
  })

  describe('Validation Errors', () => {
    test('should reject missing title', async () => {
      const event = createMockEvent({
        genre: 'Electronic',
        audioFileUrl: 'https://test-bucket.s3.amazonaws.com/track.mp3',
        fileKey: 'tracks/user123/track.mp3'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject missing genre', async () => {
      const event = createMockEvent({
        title: 'Test Track',
        audioFileUrl: 'https://test-bucket.s3.amazonaws.com/track.mp3',
        fileKey: 'tracks/user123/track.mp3'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject invalid audioFileUrl', async () => {
      const event = createMockEvent({
        title: 'Test Track',
        genre: 'Electronic',
        audioFileUrl: 'not-a-valid-url',
        fileKey: 'tracks/user123/track.mp3'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject title that is too long', async () => {
      const event = createMockEvent({
        title: 'A'.repeat(201), // 201 characters
        genre: 'Electronic',
        audioFileUrl: 'https://test-bucket.s3.amazonaws.com/track.mp3',
        fileKey: 'tracks/user123/track.mp3'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })
  })

  describe('Authentication Errors', () => {
    test('should reject unauthenticated requests', async () => {
      const event = {
        body: JSON.stringify({
          title: 'Test Track',
          genre: 'Electronic',
          audioFileUrl: 'https://test-bucket.s3.amazonaws.com/track.mp3',
          fileKey: 'tracks/user123/track.mp3'
        }),
        requestContext: {}
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(401)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('UNAUTHORIZED')
    })
  })

  describe('Database Errors', () => {
    test('should handle DynamoDB errors', async () => {
      // Ensure S3 validation passes first
      mockS3Send.mockImplementationOnce(() => Promise.resolve({
        ContentLength: 5000000,
        ContentType: 'audio/mpeg',
        LastModified: new Date(),
        Metadata: { 'duration-seconds': '180' }
      }))

      // Then make DynamoDB fail
      mockDynamoSend.mockRejectedValueOnce(new Error('DynamoDB error'))

      const event = createMockEvent({
        title: 'Test Track',
        genre: 'Electronic',
        audioFileUrl: 'https://test-bucket.s3.amazonaws.com/track.mp3',
        fileKey: 'tracks/test-user-123/2024-01-15/track.mp3'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(500)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('METADATA_ERROR')
    })

    test('should handle duplicate track condition', async () => {
      // Ensure S3 validation passes first
      mockS3Send.mockImplementationOnce(() => Promise.resolve({
        ContentLength: 5000000,
        ContentType: 'audio/mpeg',
        LastModified: new Date(),
        Metadata: { 'duration-seconds': '180' }
      }))

      const conditionalError = new Error('Conditional check failed')
      conditionalError.name = 'ConditionalCheckFailedException'
      mockDynamoSend.mockRejectedValueOnce(conditionalError)

      const event = createMockEvent({
        title: 'Duplicate Track',
        genre: 'Electronic',
        audioFileUrl: 'https://test-bucket.s3.amazonaws.com/track.mp3',
        fileKey: 'tracks/test-user-123/2024-01-15/track.mp3'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(409)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('DUPLICATE_TRACK')
    })
  })
})
