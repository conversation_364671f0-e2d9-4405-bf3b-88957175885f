---
name: 🐛 Bug Report
about: Create a report to help us improve
title: '[BUG] '
labels: ['bug', 'needs-triage']
assignees: ''
---

## 🐛 Bug Description

A clear and concise description of what the bug is.

## 🔄 Steps to Reproduce

1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## ✅ Expected Behavior

A clear and concise description of what you expected to happen.

## ❌ Actual Behavior

A clear and concise description of what actually happened.

## 📸 Screenshots

If applicable, add screenshots to help explain your problem.

## 🌍 Environment

**Frontend:**
- Browser: [e.g. Chrome, Safari]
- Version: [e.g. 22]
- Device: [e.g. Desktop, Mobile]
- OS: [e.g. Windows, macOS, iOS]

**Backend:**
- Environment: [e.g. Development, Staging, Production]
- API Version: [e.g. v1.0.0]

## 📋 Additional Context

Add any other context about the problem here.

## 🔍 Error Logs

```
Paste any relevant error logs here
```

## 🎯 Sprint & Epic

- **Sprint:** [e.g., Sprint 2]
- **Epic:** [e.g., Epic 2: AI Music Content Management]

## 🚨 Severity

- [ ] 🔥 Critical (System down, data loss)
- [ ] 🚨 High (Major feature broken)
- [ ] ⚠️ Medium (Minor feature issue)
- [ ] 📝 Low (Cosmetic issue)

## 💡 Possible Solution

If you have suggestions on how to fix the bug, please describe them here.
