# Tunami AI Music Platform - Minimum Viable Product (MVP) Technical Design Document (TDD)

**Version:** 1.0
**Date:** June 1, 2025
**Authored by:** <PERSON> (Architect), <PERSON> (Tech Lead) - AI Assistant

---

### 1. Introduction

This Technical Design Document (TDD) outlines the architectural and technical specifications for the Tunami AI Music Platform MVP. It serves as a comprehensive guide for the development team, translating the Product Requirements Document (PRD) into an actionable plan. This document encompasses the core system architecture, component-level designs, and critical non-functional requirements (NFRs) strategies.

### 2. High-Level System Architecture

Tunami adopts a **serverless-first approach** on **AWS Cloud**, designed for agility, scalability, and cost-efficiency.

* **Frontend:** React.js, Tailwind CSS (Hosted on AWS S3 via AWS CloudFront).
* **Backend Logic:** AWS Lambda (Node.js/Python).
* **API Gateway:** AWS API Gateway for all external API interactions.
* **Database:** AWS DynamoDB (NoSQL).
* **File Storage:** AWS S3 (for audio/image files).
* **User Management:** AWS Cognito.
* **Payment Processing:** Stripe.
* **Content Delivery:** AWS CloudFront.

*(Refer to the previously discussed Mermaid diagram for a visual representation of this architecture.)*

### 3. Backend Lambda Functions (Key Examples)

A granular breakdown of discrete AWS Lambda functions will drive all backend logic. Each function adheres to a single responsibility. Examples include:

* `registerUser` (User Management)
* `loginUser` (User Management)
* `getCurrentUser` (User Management)
* `uploadTrack` (Content Management - handles MP3 uploads to S3)
* `createTrackMetadata` (Content Management - stores metadata in DynamoDB)
* `getTrackDetails` (Discovery)
* `listAllTracks` (Discovery)
* `searchTracks` (Discovery)
* `likeTrack` (Community)
* `addComment` (Community)
* `createSubscription` (Monetization - integrates with Stripe)
* `handleStripeWebhook` (Monetization)
* `reportContent` (Admin)
* `takeModerationAction` (Admin - Admin-only endpoint)

### 4. DynamoDB Data Models

Tunami utilizes DynamoDB for its core data storage, designed with a single-table approach where appropriate, emphasizing efficient access patterns. All tables will use **On-Demand Capacity Mode** for MVP.

* **`TunamiUsers` Table:**
    * `PK`: `USER#<userId>`
    * `SK`: `PROFILE#<userId>`
    * Attributes: `userId`, `username`, `email`, `passwordHash` (managed by Cognito), `createdAt`, `bio`, `profileImageUrl`, `creatorStatus`, etc.
    * GSI: `email-index` (PK: `email`, SK: `PROFILE#<userId>`)
* **`TunamiTracks` Table:**
    * `PK`: `TRACK#<trackId>`
    * `SK`: `METADATA#<trackId>`
    * Attributes: `trackId`, `creatorId`, `title`, `genre`, `audioFileUrl`, `coverImageUrl`, `uploadDate`, `isPublic`, `listenCount`, `likeCount`, `commentCount`, `description`, `aiToolsUsed`.
    * GSI 1: `creatorId-uploadDate-index` (PK: `USER#<creatorId>`, SK: `TRACK#<uploadDate>`)
    * GSI 2: `isPublic-uploadDate-index` (PK: `isPublic`, SK: `TRACK#<uploadDate>`)
* **`TunamiLikes` Table:**
    * `PK`: `USER#<userId>`
    * `SK`: `LIKE#<trackId>`
    * Attributes: `userId`, `trackId`, `likedAt`.
* **`TunamiComments` Table:**
    * `PK`: `TRACK#<trackId>`
    * `SK`: `COMMENT#<commentId>`
    * Attributes: `commentId`, `trackId`, `userId`, `commentText`, `createdAt`.
* **`TunamiFollows` Table:**
    * `PK`: `USER#<followerId>`
    * `SK`: `FOLLOWING#<followedId>`
    * Attributes: `followerId`, `followedId`, `followedAt`.
* **`TunamiReports` Table:**
    * `PK`: `REPORT#<reportId>`
    * `SK`: `TARGET#<targetId>` (e.g., `TARGET#USER#<userId>` or `TARGET#TRACK#<trackId>`)
    * Attributes: `reportId`, `reporterId`, `reason`, `status` (`pending`, `resolved`, `dismissed`), `reportedAt`.
* **`TunamiSubscriptions` Table:**
    * `PK`: `USER#<userId>`
    * `SK`: `SUBSCRIPTION#<subscriptionId>`
    * Attributes: `userId`, `subscriptionId` (Stripe ID), `status`, `plan`, `startDate`, `endDate`.
* **`TunamiTransactions` Table:**
    * `PK`: `TRANSACTION#<transactionId>`
    * `SK`: `USER#<userId>`
    * Attributes: `transactionId` (Stripe ID), `userId`, `type` (`subscription`, `tip`), `amount`, `currency`, `status`, `createdAt`.

### 5. API Specifications

RESTful API endpoints will facilitate all interactions, enforcing standard HTTP methods and status codes.

* **Authentication:** JWT-based using AWS Cognito Authorizers.
* **Standard Responses:** Consistent JSON payloads for success and error states.
* **Examples:**
    * `POST /users/register`: Request: `{username, email, password}`, Response: `{userId, username, email}` (201 Created)
    * `GET /tracks/{trackId}`: Response: `{trackId, title, audioFileUrl, ...}` (200 OK)
    * `POST /tracks`: Request: `{title, genre, description, ...}`, Response: `{trackId, ...}` (201 Created)
    * `POST /payments/subscribe`: Request: `{planId, token}`, Response: `{subscriptionId, status}` (200 OK)

### 6. Deployment Strategy (CI/CD and Infrastructure as Code)

* **Infrastructure as Code (IaC):** AWS Serverless Application Model (AWS SAM) for defining and managing all AWS resources.
* **CI/CD Tool:** GitHub Actions for automated build, test, and deployment pipelines.
* **Workflow:** Automated triggers on code commit/PR for unit/integration tests, followed by staged deployments (Dev -> Staging -> Production) with manual approval gates for production.
* **Environments:** Separate, isolated AWS accounts/environments for Development, Staging, and Production.

### 7. Security Architecture Details

Security is "built-in" and layered across all components.

* **Authentication & Authorization:** AWS Cognito for user identity; API Gateway Cognito Authorizers for API access control; Least-privilege IAM roles for all Lambda functions.
* **Data Protection:** TLS/SSL for all data in transit; AWS KMS/SSE-S3 for encryption at rest for DynamoDB and S3.
* **Payment Security:** Fully offloaded to Stripe; Tunami will not store sensitive payment information.
* **Network Security:** Use of VPC for backend resources, Security Groups, AWS WAF for DDoS and common web exploits.
* **Content Integrity:** Input validation on all user inputs; administrative moderation system for reported content.
* **Secrets Management:** AWS Secrets Manager or Parameter Store for sensitive configurations.

### 8. Monitoring & Logging Strategy

Comprehensive observability using AWS native services.

* **Centralized Logging:** AWS CloudWatch Logs for all application and infrastructure logs (structured JSON logging).
* **Performance Monitoring:** AWS CloudWatch Metrics for automatic performance data (invocations, errors, duration, latency).
* **Proactive Alerting:** AWS CloudWatch Alarms on critical metrics/log patterns, notifying via Amazon SNS.
* **Cost-Efficiency:** Leverages pay-per-use model, minimizing fixed costs. X-Ray is a future consideration.

### 9. Error Handling Strategy

A robust, layered approach for resilience and user experience.

* **Frontend:** User-friendly error messages, React Error Boundaries for graceful UI degradation.
* **API Gateway:** Maps backend errors to standard HTTP status codes (4xx/5xx).
* **Backend (Lambdas):** Rigorous input validation, `try-catch` for expected errors, detailed contextual logging, Idempotency for critical operations.
* **Asynchronous Errors:** Dead-Letter Queues (DLQs) for failed asynchronous Lambda invocations.
* **Retries:** AWS SDK automatic retries for transient issues.
* **Monitoring & Alerting:** CloudWatch Alarms for critical error rates.

### 10. Cost Optimization Considerations

Designed for lean MVP operations with potential for future scaling.

* **Lambda:** Optimal memory configuration, minimal execution duration, lean deployment packages.
* **DynamoDB:** On-Demand capacity mode, efficient data modeling (queries over scans), TTL for old data.
* **S3:** S3 Standard for MVP, with future lifecycle policies for tiered storage.
* **API Gateway:** Throttling, future caching considerations.
* **CloudWatch:** Prudent log retention policies.

### 11. Scalability & Performance Optimization Strategies

Leverages serverless benefits while implementing specific optimizations.

* **Frontend:** CloudFront CDN for static assets, minification, lazy loading, browser caching.
* **Backend (Lambdas):** Automatic scaling, cold start optimization (lean packages, potentially Provisioned Concurrency for critical paths), connection reuse.
* **Database (DynamoDB):** On-Demand capacity scaling, efficient data modeling with GSIs.
* **API Gateway:** Automatic scaling, edge optimization, future caching.
* **Asynchronous Processing:** Offloads heavy tasks from synchronous API paths.

### 12. Audio File Format Strategy (MVP)

* **Decision:** For the MVP, Tunami will **stick to MP3 for both audio file uploads and primary playback/streaming.**
* **Rationale:** This decision prioritizes rapid time-to-market, minimizes initial development complexity, and significantly controls operational costs associated with storage and real-time transcoding for multiple formats.
* **Future:** Support for additional high-fidelity formats (AAC, FLAC, WAV, etc.) will be considered in post-MVP phases based on user feedback and business justification.

### 13. High-Level Sprint Roadmap (MVP Execution)

This phased approach guides the development sprints to deliver the full MVP functionality.

* **Sprint 1: Foundational Infrastructure & User Authentication/Profile**
    * *Goal:* Establish core AWS infrastructure, enable user registration and login.
    * *Key Tasks:* AWS account setup, CI/CD, Cognito, `TunamiUsers` DB, Register/Login/Profile APIs & UI.
* **Sprint 2: Core Content Management & Basic Playback**
    * *Goal:* Enable creators to upload AI-generated music (MP3) and users to play it.
    * *Key Tasks:* `uploadTrack` Lambda (S3), `createTrackMetadata` Lambda (`TunamiTracks`), `getTrackDetails`/`listAllTracks` Lambdas, "Upload Track" UI, Music Player UI, Track Details/Listing UI.
* **Sprint 3: Discovery, Search & Basic Community Features**
    * *Goal:* Allow users to find content and engage with creators and tracks.
    * *Key Tasks:* Enhanced track listing/search, `likeTrack` Lambda (`TunamiLikes`), `addComment` Lambda (`TunamiComments`), Search UI, Like/Comment UI.
* **Sprint 4: Monetization - Subscriptions & Tipping**
    * *Goal:* Integrate Stripe for core revenue generation.
    * *Key Tasks:* Stripe Webhook handling, `createCheckoutSession`, `createTippingPaymentIntent` Lambdas, `TunamiSubscriptions`/`TunamiTransactions` DBs, Subscription/Tipping UI.
* **Sprint 5: Admin & Moderation Features**
    * *Goal:* Provide tools for platform management and content safety.
    * *Key Tasks:* `reportContent`/`reportUser` Lambda (`TunamiReports`), Admin-only APIs for moderation actions, basic Admin Dashboard UI, "Report" buttons.

---

### Conclusion

This Technical Design Document concludes the architectural planning phase for the Tunami AI Music Platform MVP. It provides a solid, well-defined technical foundation, clear implementation guidelines, and a phased roadmap for development.

With this TDD, we are now ready to transition fully into the execution phase, beginning with Sprint 1.
