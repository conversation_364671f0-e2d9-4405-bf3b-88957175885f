const axios = require('axios');

async function testBackendResponse() {
  try {
    console.log('🔍 Testing backend response format...\n');
    
    // Test tracks endpoint
    console.log('📋 Testing /tracks endpoint:');
    const tracksResponse = await axios.get('http://localhost:3001/tracks');
    console.log('Response structure:', JSON.stringify(tracksResponse.data, null, 2));
    
    console.log('\n📋 Testing /tracks/sample-1 endpoint:');
    try {
      const trackResponse = await axios.get('http://localhost:3001/tracks/sample-1');
      console.log('Track response:', JSON.stringify(trackResponse.data, null, 2));
    } catch (error) {
      console.log('Track details error:', error.response?.status, error.response?.data);
    }
    
    console.log('\n📤 Testing upload endpoint:');
    const uploadResponse = await axios.post('http://localhost:3001/tracks/upload', {
      fileName: 'test.mp3',
      fileSize: 5000000,
      contentType: 'audio/mpeg'
    });
    console.log('Upload response:', JSON.stringify(uploadResponse.data, null, 2));
    
  } catch (error) {
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
  }
}

testBackendResponse();
