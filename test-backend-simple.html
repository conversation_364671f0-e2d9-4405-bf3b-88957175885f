<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel Backend Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
        .success {
            background: #10b981;
        }
        .error {
            background: #ef4444;
        }
        .result {
            background: #f3f4f6;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <h1>🚀 Sprint 5 Admin Panel Backend Test</h1>
    
    <div class="container">
        <h2>Backend Server Status</h2>
        <div id="serverStatus" class="status">Checking...</div>
        <button onclick="checkHealth()">Check Health</button>
    </div>

    <div class="container">
        <h2>Admin API Tests</h2>
        <button onclick="getStats()">Get Platform Stats</button>
        <button onclick="getReports()">Get Reports Queue</button>
        <button onclick="submitReport()">Submit Test Report</button>
        <button onclick="takeAction()">Take Moderation Action</button>
        <div id="apiResults" class="result"></div>
    </div>

    <div class="container">
        <h2>Frontend Testing Instructions</h2>
        <p><strong>Since the backend is working, here's how to test the admin panel:</strong></p>
        <ol>
            <li><strong>Open a new terminal</strong> and navigate to the frontend folder</li>
            <li><strong>Run:</strong> <code>npm run dev</code></li>
            <li><strong>Open:</strong> <a href="http://localhost:5173" target="_blank">http://localhost:5173</a></li>
            <li><strong>Login with admin email:</strong> <EMAIL></li>
            <li><strong>Navigate to:</strong> /admin to see the admin dashboard</li>
            <li><strong>Test reporting:</strong> Go to /discover and click flag icons on tracks</li>
        </ol>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001';

        async function checkHealth() {
            const statusDiv = document.getElementById('serverStatus');
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                statusDiv.className = 'status success';
                statusDiv.textContent = `✅ ${data.message}`;
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ Backend server not responding: ${error.message}`;
            }
        }

        async function getStats() {
            await testAPI('/dev/admin/stats', 'GET', null, 'Platform Stats');
        }

        async function getReports() {
            await testAPI('/dev/admin/reports?status=pending', 'GET', null, 'Reports Queue');
        }

        async function submitReport() {
            const reportData = {
                contentId: 'test-track-' + Date.now(),
                contentType: 'track',
                reason: 'inappropriate',
                description: 'Test report from HTML test page'
            };
            await testAPI('/dev/reports/content', 'POST', reportData, 'Submit Report');
        }

        async function takeAction() {
            // First get reports to find a report ID
            try {
                const reportsResponse = await fetch(`${API_BASE}/dev/admin/reports?status=pending`);
                const reportsData = await reportsResponse.json();
                
                if (reportsData.reports && reportsData.reports.length > 0) {
                    const reportId = reportsData.reports[0].id;
                    const actionData = {
                        reportId: reportId,
                        action: 'dismiss'
                    };
                    await testAPI('/dev/admin/moderation/action', 'POST', actionData, 'Take Moderation Action');
                } else {
                    document.getElementById('apiResults').textContent = 'No pending reports to take action on. Submit a report first.';
                }
            } catch (error) {
                document.getElementById('apiResults').textContent = `Error: ${error.message}`;
            }
        }

        async function testAPI(endpoint, method, body, testName) {
            const resultsDiv = document.getElementById('apiResults');
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (body) {
                    options.body = JSON.stringify(body);
                }

                const response = await fetch(`${API_BASE}${endpoint}`, options);
                const data = await response.json();
                
                resultsDiv.textContent = `✅ ${testName} Success:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultsDiv.textContent = `❌ ${testName} Failed:\n${error.message}`;
            }
        }

        // Check health on page load
        window.onload = checkHealth;
    </script>
</body>
</html>
