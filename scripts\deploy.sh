#!/bin/bash

# Tunami MVP Deployment Script
# Usage: ./scripts/deploy.sh [environment] [component]
# Example: ./scripts/deploy.sh dev backend
# Example: ./scripts/deploy.sh staging frontend
# Example: ./scripts/deploy.sh prod all

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT=${1:-dev}
COMPONENT=${2:-all}
AWS_REGION=${AWS_REGION:-us-east-1}

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    echo -e "${RED}Error: Environment must be one of: dev, staging, prod${NC}"
    exit 1
fi

# Validate component
if [[ ! "$COMPONENT" =~ ^(backend|frontend|all)$ ]]; then
    echo -e "${RED}Error: Component must be one of: backend, frontend, all${NC}"
    exit 1
fi

echo -e "${BLUE}🚀 Starting Tunami MVP deployment...${NC}"
echo -e "${BLUE}Environment: ${ENVIRONMENT}${NC}"
echo -e "${BLUE}Component: ${COMPONENT}${NC}"
echo -e "${BLUE}Region: ${AWS_REGION}${NC}"
echo ""

# Load environment configuration
CONFIG_FILE="environments/${ENVIRONMENT}/config.json"
if [[ ! -f "$CONFIG_FILE" ]]; then
    echo -e "${RED}Error: Configuration file not found: $CONFIG_FILE${NC}"
    exit 1
fi

STACK_NAME=$(jq -r '.aws.stackName' "$CONFIG_FILE")

# Function to deploy backend
deploy_backend() {
    echo -e "${YELLOW}📦 Deploying backend...${NC}"
    
    # Install dependencies
    echo "Installing backend dependencies..."
    cd backend
    npm ci
    cd ..
    
    # Build SAM application
    echo "Building SAM application..."
    sam build
    
    # Deploy to AWS
    echo "Deploying to AWS..."
    sam deploy \
        --stack-name "$STACK_NAME" \
        --parameter-overrides Environment="$ENVIRONMENT" \
        --capabilities CAPABILITY_IAM \
        --region "$AWS_REGION" \
        --no-confirm-changeset \
        --no-fail-on-empty-changeset
    
    # Get API Gateway URL
    API_URL=$(aws cloudformation describe-stacks \
        --stack-name "$STACK_NAME" \
        --query 'Stacks[0].Outputs[?OutputKey==`TunamiApiUrl`].OutputValue' \
        --output text \
        --region "$AWS_REGION")
    
    echo -e "${GREEN}✅ Backend deployed successfully!${NC}"
    echo -e "${GREEN}API URL: $API_URL${NC}"
    
    # Save API URL for frontend deployment
    echo "$API_URL" > .api-url-temp
}

# Function to deploy frontend
deploy_frontend() {
    echo -e "${YELLOW}🌐 Deploying frontend...${NC}"
    
    # Get API URL (from backend deployment or existing stack)
    if [[ -f ".api-url-temp" ]]; then
        API_URL=$(cat .api-url-temp)
        rm .api-url-temp
    else
        API_URL=$(aws cloudformation describe-stacks \
            --stack-name "$STACK_NAME" \
            --query 'Stacks[0].Outputs[?OutputKey==`TunamiApiUrl`].OutputValue' \
            --output text \
            --region "$AWS_REGION" 2>/dev/null || echo "")
    fi
    
    if [[ -z "$API_URL" ]]; then
        echo -e "${RED}Error: Could not retrieve API URL. Deploy backend first.${NC}"
        exit 1
    fi
    
    # Install dependencies and build
    echo "Installing frontend dependencies..."
    cd frontend
    npm ci
    
    # Create environment file
    echo "VITE_API_BASE_URL=$API_URL" > .env.production
    
    # Build frontend
    echo "Building frontend..."
    npm run build
    cd ..
    
    # Get S3 bucket and CloudFront distribution from config
    S3_BUCKET=$(jq -r '.s3.staticWebsiteBucket' "$CONFIG_FILE")
    
    # Deploy to S3
    echo "Deploying to S3..."
    aws s3 sync frontend/dist/ "s3://$S3_BUCKET" --delete
    
    # Get CloudFront distribution ID (if exists)
    CLOUDFRONT_ID=$(aws cloudfront list-distributions \
        --query "DistributionList.Items[?Comment=='$(jq -r '.cloudfront.distributionComment' "$CONFIG_FILE")'].Id" \
        --output text 2>/dev/null || echo "")
    
    if [[ -n "$CLOUDFRONT_ID" && "$CLOUDFRONT_ID" != "None" ]]; then
        echo "Invalidating CloudFront cache..."
        aws cloudfront create-invalidation \
            --distribution-id "$CLOUDFRONT_ID" \
            --paths "/*" > /dev/null
        echo -e "${GREEN}✅ CloudFront cache invalidated${NC}"
    fi
    
    echo -e "${GREEN}✅ Frontend deployed successfully!${NC}"
    echo -e "${GREEN}S3 Bucket: $S3_BUCKET${NC}"
    if [[ -n "$CLOUDFRONT_ID" && "$CLOUDFRONT_ID" != "None" ]]; then
        echo -e "${GREEN}CloudFront Distribution: $CLOUDFRONT_ID${NC}"
    fi
}

# Function to run tests
run_tests() {
    echo -e "${YELLOW}🧪 Running tests...${NC}"
    
    # Backend tests
    echo "Running backend tests..."
    cd backend
    npm test
    cd ..
    
    # Frontend tests
    echo "Running frontend tests..."
    cd frontend
    npm test -- --run
    cd ..
    
    echo -e "${GREEN}✅ All tests passed!${NC}"
}

# Main deployment logic
case $COMPONENT in
    "backend")
        run_tests
        deploy_backend
        ;;
    "frontend")
        deploy_frontend
        ;;
    "all")
        run_tests
        deploy_backend
        deploy_frontend
        ;;
esac

echo ""
echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
echo -e "${GREEN}Environment: $ENVIRONMENT${NC}"
echo -e "${GREEN}Component: $COMPONENT${NC}"

# Cleanup
if [[ -f ".api-url-temp" ]]; then
    rm .api-url-temp
fi
