import React, { useState } from 'react'
import { Crown, Star } from 'lucide-react'
import SubscriptionModal from './SubscriptionModal'

const SubscriptionButton = ({ 
  isSubscribed = false, 
  size = 'md',
  className = '' 
}) => {
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false)

  // Size variants
  const sizeClasses = {
    sm: 'px-3 py-1 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  }

  const iconSizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  }

  const handleSubscriptionClick = () => {
    if (isSubscribed) {
      // TODO: Navigate to subscription management page
      console.log('Navigate to subscription management')
    } else {
      setShowSubscriptionModal(true)
    }
  }

  const handleSubscriptionSuccess = (subscriptionData) => {
    setShowSubscriptionModal(false)
    // TODO: Update user subscription status in context/state
    console.log('Subscription successful:', subscriptionData)
  }

  const handleSubscriptionCancel = () => {
    setShowSubscriptionModal(false)
  }

  if (isSubscribed) {
    return (
      <button
        onClick={handleSubscriptionClick}
        className={`
          flex items-center gap-2 rounded-full transition-all duration-200
          bg-gradient-to-r from-yellow-400 to-yellow-600 text-white
          hover:from-yellow-500 hover:to-yellow-700
          ${sizeClasses[size]}
          ${className}
        `}
        title="Manage your Tunami Supporter subscription"
      >
        <Crown className={iconSizeClasses[size]} />
        <span className="font-medium">Supporter</span>
      </button>
    )
  }

  return (
    <>
      <button
        onClick={handleSubscriptionClick}
        className={`
          flex items-center gap-2 rounded-full transition-all duration-200
          bg-gradient-to-r from-purple-600 to-blue-600 text-white
          hover:from-purple-700 hover:to-blue-700
          hover:scale-105 active:scale-95
          ${sizeClasses[size]}
          ${className}
        `}
        title="Become a Tunami Supporter"
      >
        <Star className={iconSizeClasses[size]} />
        <span className="font-medium">
          {size === 'sm' ? 'Support' : 'Become a Supporter'}
        </span>
      </button>

      {/* Subscription Modal */}
      {showSubscriptionModal && (
        <SubscriptionModal
          onSuccess={handleSubscriptionSuccess}
          onCancel={handleSubscriptionCancel}
        />
      )}
    </>
  )
}

export default SubscriptionButton
