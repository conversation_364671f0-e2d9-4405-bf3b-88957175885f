import React, { useState } from 'react'
import { DollarSign, Heart, Gift } from 'lucide-react'
import { toast } from 'react-hot-toast'
import TipModal from './TipModal'

const TipButton = ({ 
  trackId, 
  creatorId, 
  creatorUsername, 
  trackTitle,
  size = 'md',
  variant = 'default',
  className = '' 
}) => {
  const [showTipModal, setShowTipModal] = useState(false)

  // Size variants
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  }

  const buttonSizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  }

  // Variant styles
  const variantClasses = {
    default: 'bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white',
    outline: 'border-2 border-green-500 text-green-600 hover:bg-green-50',
    minimal: 'text-green-600 hover:text-green-700 hover:bg-green-50'
  }

  const handleTipClick = (e) => {
    e.stopPropagation() // Prevent event bubbling
    setShowTipModal(true)
  }

  const handleTipSuccess = (tipData) => {
    setShowTipModal(false)
    toast.success(
      `🎉 Tip of $${(tipData.amount / 100).toFixed(2)} sent to ${creatorUsername}!`,
      { duration: 3000 }
    )
  }

  const handleTipCancel = () => {
    setShowTipModal(false)
  }

  return (
    <>
      <button
        onClick={handleTipClick}
        className={`
          flex items-center gap-2 rounded-full transition-all duration-200
          ${buttonSizeClasses[size]}
          ${variantClasses[variant]}
          hover:scale-105 active:scale-95
          focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2
          ${className}
        `}
        title={`Tip ${creatorUsername} for "${trackTitle}"`}
      >
        {variant === 'minimal' ? (
          <Gift className={sizeClasses[size]} />
        ) : (
          <DollarSign className={sizeClasses[size]} />
        )}
        {size !== 'sm' && (
          <span className="font-medium">
            {variant === 'minimal' ? 'Tip' : 'Tip Creator'}
          </span>
        )}
      </button>

      {/* Tip Modal */}
      {showTipModal && (
        <TipModal
          trackId={trackId}
          creatorId={creatorId}
          creatorUsername={creatorUsername}
          trackTitle={trackTitle}
          onSuccess={handleTipSuccess}
          onCancel={handleTipCancel}
        />
      )}
    </>
  )
}

export default TipButton
