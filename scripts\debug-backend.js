// Debug Backend Server with Error Handling
console.log('🔍 Starting debug backend server...')

try {
  const express = require('express')
  console.log('✅ Express loaded successfully')
  
  const cors = require('cors')
  console.log('✅ CORS loaded successfully')
  
  const app = express()
  const PORT = 3001
  
  console.log('🔧 Setting up middleware...')
  app.use(cors())
  app.use(express.json())
  console.log('✅ Middleware configured')
  
  // Simple health check
  app.get('/health', (req, res) => {
    console.log('📡 Health check requested')
    res.json({ status: 'healthy', message: 'Debug backend running', timestamp: new Date().toISOString() })
  })
  
  // Mock stats
  app.get('/dev/admin/stats', (req, res) => {
    console.log('📊 Stats requested')
    res.json({
      totalReports: 5,
      pendingReports: 2,
      resolvedReports: 3,
      totalUsers: 100,
      totalTracks: 50,
      activeUsers: 25
    })
  })
  
  // Mock reports
  app.get('/dev/admin/reports', (req, res) => {
    console.log('📋 Reports requested')
    res.json({
      reports: [
        {
          id: 'test-report-1',
          reason: 'inappropriate',
          status: 'pending',
          reportedAt: new Date().toISOString(),
          reporter: { username: 'testuser', email: '<EMAIL>' },
          target: { id: 'track-1', title: 'Test Track', artist: 'Test Artist', type: 'track' },
          description: 'Test report',
          priority: 'medium'
        }
      ],
      pagination: { hasMore: false, count: 1 }
    })
  })
  
  // Submit report
  app.post('/dev/reports/content', (req, res) => {
    console.log('📝 Content report submitted:', req.body)
    res.status(201).json({ 
      message: 'Report submitted successfully', 
      reportId: 'test-' + Date.now(),
      status: 'pending'
    })
  })
  
  // Moderation action
  app.post('/dev/admin/moderation/action', (req, res) => {
    console.log('⚖️ Moderation action:', req.body)
    const { reportId, action } = req.body
    res.json({ 
      message: `Action ${action} completed successfully`,
      reportId,
      action,
      status: 'resolved'
    })
  })
  
  // Error handling middleware
  app.use((err, req, res, next) => {
    console.error('❌ Server error:', err)
    res.status(500).json({ error: 'Internal server error', message: err.message })
  })
  
  // Start server with error handling
  const server = app.listen(PORT, (err) => {
    if (err) {
      console.error('❌ Failed to start server:', err)
      process.exit(1)
    }
    console.log(`🚀 Debug Backend Server running on http://localhost:${PORT}`)
    console.log('📊 Available endpoints:')
    console.log('   GET  /health - Health check')
    console.log('   GET  /dev/admin/stats - Platform stats')
    console.log('   GET  /dev/admin/reports - Reports queue')
    console.log('   POST /dev/reports/content - Submit report')
    console.log('   POST /dev/admin/moderation/action - Take action')
    console.log('\n✅ Backend ready for testing!')
  })
  
  // Handle server errors
  server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
      console.error(`❌ Port ${PORT} is already in use`)
      console.log('🔧 Trying to kill existing process...')
      process.exit(1)
    } else {
      console.error('❌ Server error:', err)
    }
  })
  
  // Graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...')
    server.close(() => {
      console.log('✅ Server closed')
      process.exit(0)
    })
  })
  
} catch (error) {
  console.error('❌ Failed to start server:', error)
  console.error('Stack trace:', error.stack)
  process.exit(1)
}
