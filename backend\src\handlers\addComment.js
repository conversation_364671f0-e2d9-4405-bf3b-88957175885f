const { DynamoDBClient, PutItemCommand, GetItemCommand, UpdateItemCommand } = require('@aws-sdk/client-dynamodb')
const { marshall } = require('@aws-sdk/util-dynamodb')
const { v4: uuidv4 } = require('uuid')
const { successResponse, errorResponse } = require('../utils/responseHelper')
const { validateInput } = require('../utils/validation')
const Joi = require('joi')

const dynamoClient = new DynamoDBClient({ region: process.env.AWS_REGION })

// Validation schema for comment
const commentSchema = Joi.object({
  content: Joi.string().required().min(1).max(500).trim(),
  trackId: Joi.string().required()
})

/**
 * Lambda handler for adding comments to tracks
 */
exports.handler = async (event) => {
  console.log('Add comment request:', JSON.stringify(event, null, 2))

  try {
    // Extract user ID from Cognito authorizer
    const userId = event.requestContext?.authorizer?.claims?.sub
    if (!userId) {
      return errorResponse('User not authenticated', 401, 'UNAUTHORIZED')
    }

    // Parse and validate request body
    const body = JSON.parse(event.body || '{}')
    
    // Add trackId from path parameters if not in body
    if (event.pathParameters?.trackId) {
      body.trackId = event.pathParameters.trackId
    }

    const validation = validateInput(body, commentSchema)
    
    if (!validation.isValid) {
      return errorResponse('Invalid input data', 400, 'VALIDATION_ERROR', validation.errors)
    }

    const { content, trackId } = validation.value

    // Check if track exists
    const trackExists = await checkTrackExists(trackId)
    if (!trackExists) {
      return errorResponse('Track not found', 404, 'TRACK_NOT_FOUND')
    }

    // Create comment
    const commentId = uuidv4()
    const createdAt = new Date().toISOString()

    const commentData = {
      commentId,
      trackId,
      userId,
      content,
      createdAt,
      updatedAt: createdAt
    }

    // Store comment in DynamoDB
    const putCommand = new PutItemCommand({
      TableName: process.env.COMMENTS_TABLE_NAME,
      Item: marshall(commentData),
      ConditionExpression: 'attribute_not_exists(commentId)'
    })

    await dynamoClient.send(putCommand)

    // Update track comment count
    await updateTrackCommentCount(trackId, 1)

    console.log('Comment added successfully:', commentId)

    return successResponse({
      commentId,
      trackId,
      userId,
      content,
      createdAt
    }, 'Comment added successfully')

  } catch (error) {
    console.error('Add comment error:', error)
    
    if (error.name === 'ConditionalCheckFailedException') {
      return errorResponse('Comment already exists', 409, 'DUPLICATE_COMMENT')
    }
    
    return errorResponse('Failed to add comment', 500, 'COMMENT_ERROR')
  }
}

/**
 * Check if track exists
 */
async function checkTrackExists(trackId) {
  try {
    const getCommand = new GetItemCommand({
      TableName: process.env.TRACKS_TABLE_NAME,
      Key: {
        trackId: { S: trackId }
      }
    })

    const result = await dynamoClient.send(getCommand)
    return !!result.Item
  } catch (error) {
    console.error('Error checking track existence:', error)
    return false
  }
}

/**
 * Update track comment count
 */
async function updateTrackCommentCount(trackId, increment) {
  try {
    const updateCommand = new UpdateItemCommand({
      TableName: process.env.TRACKS_TABLE_NAME,
      Key: {
        trackId: { S: trackId }
      },
      UpdateExpression: 'ADD commentCount :increment',
      ExpressionAttributeValues: {
        ':increment': { N: increment.toString() }
      }
    })

    await dynamoClient.send(updateCommand)
  } catch (error) {
    console.error('Error updating comment count:', error)
    // Don't throw error as comment was already saved
  }
}
