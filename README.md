# Tunami AI Music Platform MVP

**"For entertaining Humanity, Created by AI"**

A dedicated platform for discovering, sharing, and experiencing AI-generated music, fostering a vibrant community around the intersection of artificial intelligence and musical creativity.

## 🎵 Project Overview

Tunami is a serverless-first AI music platform built on AWS, designed to connect AI music creators with early adopter listeners. The platform enables creators to upload, manage, and monetize their AI-generated tracks while providing listeners with intuitive discovery and playback experiences.

**Current Status:** Sprint 1 Complete ✅ | Sprint 1.5 In Progress 🔄 (GitHub Actions & CI/CD)

## 🏗️ Architecture

### Backend
- **AWS Lambda** - Serverless compute for API endpoints
- **AWS API Gateway** - RESTful API management
- **AWS Cognito** - User authentication and authorization
- **AWS DynamoDB** - NoSQL database for user data
- **AWS SAM** - Infrastructure as Code framework

### Frontend
- **React 18** - Modern UI library
- **Vite** - Fast build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **React Router** - Client-side routing
- **React Hook Form** - Form state management
- **Axios** - HTTP client

## 🚀 Features Implemented (Sprint 1)

### ✅ Infrastructure (Tasks 1.1-1.3)
- [x] AWS SAM template with Cognito User Pool
- [x] DynamoDB table for user data
- [x] API Gateway with CORS configuration
- [x] GitHub Actions CI/CD pipeline

### ✅ Backend Development (Tasks 2.1-2.3)
- [x] **POST /users/register** - User registration endpoint
- [x] **POST /users/login** - User authentication endpoint
- [x] **GET /users/me** - Get current user profile (protected)
- [x] Input validation with Joi
- [x] Error handling and standardized responses
- [x] Unit tests for Lambda functions

### ✅ Frontend Development (Tasks 3.1-3.4)
- [x] React application with modern tooling
- [x] User registration form with validation
- [x] User login form with authentication
- [x] User profile dashboard
- [x] Protected routes and authentication state management
- [x] Responsive design with Tailwind CSS

## 📁 Project Structure

```
tunami-mvp/
├── backend/                    # Backend Lambda functions
│   ├── src/
│   │   ├── handlers/          # Lambda function handlers
│   │   ├── services/          # Business logic services
│   │   ├── utils/             # Utility functions
│   │   └── __tests__/         # Unit tests
│   └── package.json
├── frontend/                   # React frontend application
│   ├── src/
│   │   ├── components/        # Reusable UI components
│   │   ├── pages/             # Page components
│   │   ├── contexts/          # React contexts
│   │   ├── services/          # API services
│   │   └── config/            # Configuration files
│   └── package.json
├── .github/workflows/          # CI/CD pipelines
├── template.yaml              # AWS SAM template
└── README.md
```

## 🛠️ Setup and Development

### Prerequisites
- Node.js 18+
- npm 8+
- Git
- Docker Desktop (optional, for full AWS services)

### 🚀 Quick Start (Local Development)

**Option 1: Simple Setup (Recommended for testing)**
```bash
# Clone and install dependencies
git clone https://github.com/Vinodmurkute/tunami-mvp.git
cd tunami-mvp
npm run install:all

# Start local development (Windows)
start-local-dev.bat

# Or manually start both servers
npm run setup:local-env
npm run start:backend-express  # In one terminal
cd frontend && npm run dev     # In another terminal
```

**Option 2: Full AWS Local Services (with Docker)**
```bash
# Install dependencies
npm run install:all

# Start with full local AWS services
npm run start:local-services  # Starts DynamoDB Local, LocalStack
npm run dev:local            # Starts backend and frontend
```

### 🌐 Access Points
- **Frontend Application**: http://localhost:5173
- **Backend API**: http://localhost:3001
- **API Health Check**: http://localhost:3001/health

### 🧪 Test Users (Local Development)
| Email | Password | Role |
|-------|----------|------|
| <EMAIL> | TestPass123! | User |
| <EMAIL> | TestPass123! | User |
| <EMAIL> | AdminPass123! | Admin |

### 📖 Detailed Setup Guide
For comprehensive setup instructions, see [Local Development Setup Guide](docs/Local_Development_Setup.md)

## 🔧 Configuration

### Environment Variables

#### Backend (Lambda)
- `USER_POOL_ID` - Cognito User Pool ID
- `USER_POOL_CLIENT_ID` - Cognito User Pool Client ID
- `USERS_TABLE_NAME` - DynamoDB table name
- `AWS_REGION` - AWS region

#### Frontend
- `VITE_API_BASE_URL` - API Gateway base URL

### GitHub Secrets

For CI/CD pipelines, configure these secrets in your GitHub repository:

- `AWS_ACCESS_KEY_ID`
- `AWS_SECRET_ACCESS_KEY`
- `DEV_API_BASE_URL`
- `DEV_S3_BUCKET`
- `DEV_CLOUDFRONT_DISTRIBUTION_ID`
- `STAGING_API_BASE_URL`
- `STAGING_S3_BUCKET`
- `STAGING_CLOUDFRONT_DISTRIBUTION_ID`

## 🧪 Testing

### Backend Tests
```bash
cd backend
npm test                    # Run all tests
npm run test:watch         # Run tests in watch mode
npm run test:coverage      # Run tests with coverage
```

### Frontend Tests
```bash
cd frontend
npm test                    # Run all tests
npm run test:ui            # Run tests with UI
npm run test:coverage      # Run tests with coverage
```

## 📚 API Documentation

### Authentication Endpoints

#### Register User
```http
POST /users/register
Content-Type: application/json

{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "SecurePass123"
}
```

#### Login User
```http
POST /users/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePass123"
}
```

#### Get Current User
```http
GET /users/me
Authorization: Bearer <access_token>
```

## 🚀 Deployment

### Automatic Deployment
- **Development**: Push to `develop` branch
- **Staging**: Push to `main` branch

### Manual Deployment

#### Backend
```bash
sam build
sam deploy --parameter-overrides Environment=dev
```

#### Frontend
```bash
cd frontend
npm run build
aws s3 sync dist/ s3://your-bucket-name
```

## 🔒 Security Features

- Password validation (8+ chars, uppercase, lowercase, number)
- JWT token-based authentication
- CORS configuration
- Input validation and sanitization
- Secure password storage with Cognito

## 📈 Next Steps (Sprint 2)

- User profile editing
- Password reset functionality
- Email verification
- Social login integration
- Advanced user management features

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
