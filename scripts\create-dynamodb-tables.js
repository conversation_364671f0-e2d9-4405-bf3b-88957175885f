#!/usr/bin/env node

/**
 * Create DynamoDB Tables Script for Local Development
 * This script creates all required DynamoDB tables for local development
 */

const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { CreateTableCommand, ListTablesCommand } = require('@aws-sdk/client-dynamodb');

// Configure DynamoDB client for local development
const dynamoClient = new DynamoDBClient({
  region: 'us-east-1',
  endpoint: 'http://localhost:8000',
  credentials: {
    accessKeyId: 'dummy',
    secretAccessKey: 'dummy'
  }
});

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Table definitions
const tableDefinitions = [
  {
    TableName: 'TunamiUsers-local',
    KeySchema: [
      { AttributeName: 'userId', KeyType: 'HASH' }
    ],
    AttributeDefinitions: [
      { AttributeName: 'userId', AttributeType: 'S' },
      { AttributeName: 'email', AttributeType: 'S' }
    ],
    GlobalSecondaryIndexes: [
      {
        IndexName: 'EmailIndex',
        KeySchema: [
          { AttributeName: 'email', KeyType: 'HASH' }
        ],
        Projection: { ProjectionType: 'ALL' },
        BillingMode: 'PAY_PER_REQUEST'
      }
    ],
    BillingMode: 'PAY_PER_REQUEST'
  },
  {
    TableName: 'TunamiTracks-local',
    KeySchema: [
      { AttributeName: 'trackId', KeyType: 'HASH' }
    ],
    AttributeDefinitions: [
      { AttributeName: 'trackId', AttributeType: 'S' },
      { AttributeName: 'creatorId', AttributeType: 'S' },
      { AttributeName: 'uploadDate', AttributeType: 'S' },
      { AttributeName: 'isPublic', AttributeType: 'S' }
    ],
    GlobalSecondaryIndexes: [
      {
        IndexName: 'CreatorIdUploadDateIndex',
        KeySchema: [
          { AttributeName: 'creatorId', KeyType: 'HASH' },
          { AttributeName: 'uploadDate', KeyType: 'RANGE' }
        ],
        Projection: { ProjectionType: 'ALL' },
        BillingMode: 'PAY_PER_REQUEST'
      },
      {
        IndexName: 'IsPublicUploadDateIndex',
        KeySchema: [
          { AttributeName: 'isPublic', KeyType: 'HASH' },
          { AttributeName: 'uploadDate', KeyType: 'RANGE' }
        ],
        Projection: { ProjectionType: 'ALL' },
        BillingMode: 'PAY_PER_REQUEST'
      }
    ],
    BillingMode: 'PAY_PER_REQUEST'
  },
  {
    TableName: 'TunamiLikes-local',
    KeySchema: [
      { AttributeName: 'userId', KeyType: 'HASH' },
      { AttributeName: 'trackId', KeyType: 'RANGE' }
    ],
    AttributeDefinitions: [
      { AttributeName: 'userId', AttributeType: 'S' },
      { AttributeName: 'trackId', AttributeType: 'S' }
    ],
    GlobalSecondaryIndexes: [
      {
        IndexName: 'TrackIdUserIdIndex',
        KeySchema: [
          { AttributeName: 'trackId', KeyType: 'HASH' },
          { AttributeName: 'userId', KeyType: 'RANGE' }
        ],
        Projection: { ProjectionType: 'ALL' },
        BillingMode: 'PAY_PER_REQUEST'
      }
    ],
    BillingMode: 'PAY_PER_REQUEST'
  },
  {
    TableName: 'TunamiComments-local',
    KeySchema: [
      { AttributeName: 'commentId', KeyType: 'HASH' }
    ],
    AttributeDefinitions: [
      { AttributeName: 'commentId', AttributeType: 'S' },
      { AttributeName: 'trackId', AttributeType: 'S' },
      { AttributeName: 'createdAt', AttributeType: 'S' }
    ],
    GlobalSecondaryIndexes: [
      {
        IndexName: 'TrackIdCreatedAtIndex',
        KeySchema: [
          { AttributeName: 'trackId', KeyType: 'HASH' },
          { AttributeName: 'createdAt', KeyType: 'RANGE' }
        ],
        Projection: { ProjectionType: 'ALL' },
        BillingMode: 'PAY_PER_REQUEST'
      }
    ],
    BillingMode: 'PAY_PER_REQUEST'
  },
  {
    TableName: 'TunamiFollows-local',
    KeySchema: [
      { AttributeName: 'followerId', KeyType: 'HASH' },
      { AttributeName: 'followingId', KeyType: 'RANGE' }
    ],
    AttributeDefinitions: [
      { AttributeName: 'followerId', AttributeType: 'S' },
      { AttributeName: 'followingId', AttributeType: 'S' }
    ],
    GlobalSecondaryIndexes: [
      {
        IndexName: 'FollowingIdFollowerIdIndex',
        KeySchema: [
          { AttributeName: 'followingId', KeyType: 'HASH' },
          { AttributeName: 'followerId', KeyType: 'RANGE' }
        ],
        Projection: { ProjectionType: 'ALL' },
        BillingMode: 'PAY_PER_REQUEST'
      }
    ],
    BillingMode: 'PAY_PER_REQUEST'
  }
];

async function createTable(tableDefinition) {
  try {
    const command = new CreateTableCommand(tableDefinition);
    await dynamoClient.send(command);
    log(`✅ Created table: ${tableDefinition.TableName}`, 'green');
  } catch (error) {
    if (error.name === 'ResourceInUseException') {
      log(`ℹ️  Table already exists: ${tableDefinition.TableName}`, 'yellow');
    } else {
      log(`❌ Error creating table ${tableDefinition.TableName}: ${error.message}`, 'red');
      throw error;
    }
  }
}

async function main() {
  try {
    log('📊 Creating DynamoDB tables for local development...', 'cyan');
    log('', 'reset');

    for (const tableDefinition of tableDefinitions) {
      await createTable(tableDefinition);
    }

    log('', 'reset');
    log('✅ All DynamoDB tables created successfully!', 'green');

  } catch (error) {
    log(`❌ Error creating DynamoDB tables: ${error.message}`, 'red');
    process.exit(1);
  }
}

main();
