const { handler } = require('../handlers/registerUser')
const CognitoService = require('../services/cognitoService')
const DynamoService = require('../services/dynamoService')

// Mock the services
jest.mock('../services/cognitoService')
jest.mock('../services/dynamoService')

describe('registerUser Lambda Handler', () => {
  let mockCognitoService
  let mockDynamoService

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks()
    
    // Create mock instances
    mockCognitoService = {
      createUser: jest.fn()
    }
    mockDynamoService = {
      getUserByEmail: jest.fn(),
      createUser: jest.fn()
    }
    
    // Mock the constructor calls
    CognitoService.mockImplementation(() => mockCognitoService)
    DynamoService.mockImplementation(() => mockDynamoService)
  })

  const createEvent = (body) => ({
    body: JSON.stringify(body),
    requestContext: {},
    headers: {}
  })

  describe('Successful registration', () => {
    it('should register a new user successfully', async () => {
      const requestBody = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'TestPass123'
      }

      const mockCognitoUser = {
        userId: 'cognito-user-id',
        email: '<EMAIL>',
        username: 'testuser'
      }

      const mockDynamoUser = {
        userId: 'test-uuid',
        username: 'testuser',
        email: '<EMAIL>',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        isActive: true
      }

      // Setup mocks
      mockDynamoService.getUserByEmail.mockResolvedValue(null)
      mockCognitoService.createUser.mockResolvedValue(mockCognitoUser)
      mockDynamoService.createUser.mockResolvedValue(mockDynamoUser)

      const event = createEvent(requestBody)
      const result = await handler(event)

      expect(result.statusCode).toBe(201)
      
      const responseBody = JSON.parse(result.body)
      expect(responseBody.success).toBe(true)
      expect(responseBody.data).toMatchObject({
        userId: mockDynamoUser.userId,
        username: mockDynamoUser.username,
        email: mockDynamoUser.email,
        createdAt: mockDynamoUser.createdAt
      })

      // Verify service calls
      expect(mockDynamoService.getUserByEmail).toHaveBeenCalledWith('<EMAIL>')
      expect(mockCognitoService.createUser).toHaveBeenCalledWith('testuser', '<EMAIL>', 'TestPass123')
      expect(mockDynamoService.createUser).toHaveBeenCalled()
    })
  })

  describe('Validation errors', () => {
    it('should return validation error for invalid email', async () => {
      const requestBody = {
        username: 'testuser',
        email: 'invalid-email',
        password: 'TestPass123'
      }

      const event = createEvent(requestBody)
      const result = await handler(event)

      expect(result.statusCode).toBe(400)
      
      const responseBody = JSON.parse(result.body)
      expect(responseBody.success).toBe(false)
      expect(responseBody.error.code).toBe('VALIDATION_ERROR')
    })

    it('should return validation error for weak password', async () => {
      const requestBody = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'weak'
      }

      const event = createEvent(requestBody)
      const result = await handler(event)

      expect(result.statusCode).toBe(400)
      
      const responseBody = JSON.parse(result.body)
      expect(responseBody.success).toBe(false)
      expect(responseBody.error.code).toBe('VALIDATION_ERROR')
    })

    it('should return validation error for invalid username', async () => {
      const requestBody = {
        username: 'ab', // Too short
        email: '<EMAIL>',
        password: 'TestPass123'
      }

      const event = createEvent(requestBody)
      const result = await handler(event)

      expect(result.statusCode).toBe(400)
      
      const responseBody = JSON.parse(result.body)
      expect(responseBody.success).toBe(false)
      expect(responseBody.error.code).toBe('VALIDATION_ERROR')
    })
  })

  describe('User already exists', () => {
    it('should return error when user already exists in DynamoDB', async () => {
      const requestBody = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'TestPass123'
      }

      const existingUser = {
        userId: 'existing-user-id',
        email: '<EMAIL>'
      }

      mockDynamoService.getUserByEmail.mockResolvedValue(existingUser)

      const event = createEvent(requestBody)
      const result = await handler(event)

      expect(result.statusCode).toBe(409)
      
      const responseBody = JSON.parse(result.body)
      expect(responseBody.success).toBe(false)
      expect(responseBody.error.code).toBe('USER_EXISTS')
    })
  })

  describe('Service errors', () => {
    it('should handle Cognito service errors', async () => {
      const requestBody = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'TestPass123'
      }

      mockDynamoService.getUserByEmail.mockResolvedValue(null)
      mockCognitoService.createUser.mockRejectedValue(new Error('Cognito error'))

      const event = createEvent(requestBody)
      const result = await handler(event)

      expect(result.statusCode).toBe(500)
      
      const responseBody = JSON.parse(result.body)
      expect(responseBody.success).toBe(false)
      expect(responseBody.error.code).toBe('COGNITO_ERROR')
    })

    it('should handle DynamoDB service errors', async () => {
      const requestBody = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'TestPass123'
      }

      const mockCognitoUser = {
        userId: 'cognito-user-id',
        email: '<EMAIL>',
        username: 'testuser'
      }

      mockDynamoService.getUserByEmail.mockResolvedValue(null)
      mockCognitoService.createUser.mockResolvedValue(mockCognitoUser)
      mockDynamoService.createUser.mockRejectedValue(new Error('DynamoDB error'))

      const event = createEvent(requestBody)
      const result = await handler(event)

      expect(result.statusCode).toBe(500)
      
      const responseBody = JSON.parse(result.body)
      expect(responseBody.success).toBe(false)
      expect(responseBody.error.code).toBe('DATABASE_ERROR')
    })
  })

  describe('Invalid request format', () => {
    it('should handle invalid JSON in request body', async () => {
      const event = {
        body: 'invalid json',
        requestContext: {},
        headers: {}
      }

      const result = await handler(event)

      expect(result.statusCode).toBe(400)
      
      const responseBody = JSON.parse(result.body)
      expect(responseBody.success).toBe(false)
      expect(responseBody.error.code).toBe('INVALID_JSON')
    })

    it('should handle missing request body', async () => {
      const event = {
        requestContext: {},
        headers: {}
      }

      const result = await handler(event)

      expect(result.statusCode).toBe(400)
      
      const responseBody = JSON.parse(result.body)
      expect(responseBody.success).toBe(false)
      expect(responseBody.error.code).toBe('VALIDATION_ERROR')
    })
  })
})
