// Enhanced Local Backend - Mirrors AWS Lambda Functions
const express = require('express')
const cors = require('cors')
const { v4: uuidv4 } = require('uuid')

const app = express()
const PORT = 3001

// Middleware
app.use(cors())
app.use(express.json())

console.log('🚀 Starting Enhanced Local Backend...')

// In-memory data store (simulating DynamoDB)
const dataStore = {
  users: new Map(),
  tracks: new Map(),
  reports: new Map(),
  likes: new Map(),
  comments: new Map(),
  subscriptions: new Map()
}

// Initialize with sample data
const initializeData = () => {
  // Sample users
  dataStore.users.set('admin-user-1', {
    userId: 'admin-user-1',
    email: '<EMAIL>',
    username: 'vinodadmin',
    isAdmin: true,
    createdAt: new Date().toISOString()
  })
  
  dataStore.users.set('user-1', {
    userId: 'user-1',
    email: '<EMAIL>',
    username: 'musiclover123',
    isAdmin: false,
    createdAt: new Date().toISOString()
  })

  // Sample tracks
  dataStore.tracks.set('track-1', {
    trackId: 'track-1',
    title: 'AI Symphony No. 1',
    description: 'A beautiful AI-generated symphony',
    genre: 'Classical',
    creatorId: 'user-1',
    creator: { username: 'musiclover123' },
    uploadDate: new Date().toISOString(),
    likeCount: 15,
    commentCount: 3,
    listenCount: 127,
    tags: ['ai-generated', 'symphony', 'classical'],
    aiToolsUsed: ['Suno AI', 'AIVA']
  })

  dataStore.tracks.set('track-2', {
    trackId: 'track-2',
    title: 'Electronic Dreams',
    description: 'Futuristic electronic beats',
    genre: 'Electronic',
    creatorId: 'user-1',
    creator: { username: 'musiclover123' },
    uploadDate: new Date().toISOString(),
    likeCount: 8,
    commentCount: 1,
    listenCount: 89,
    tags: ['electronic', 'futuristic', 'beats'],
    aiToolsUsed: ['Boomy', 'Amper Music']
  })

  // Sample reports
  dataStore.reports.set('report-1', {
    reportId: 'report-1',
    reporterId: 'user-1',
    contentId: 'track-1',
    contentType: 'track',
    reason: 'inappropriate',
    description: 'This track contains inappropriate content',
    status: 'pending',
    priority: 'medium',
    reportedAt: new Date().toISOString(),
    reporter: { username: 'musiclover123', email: '<EMAIL>' },
    target: {
      id: 'track-1',
      title: 'AI Symphony No. 1',
      artist: 'musiclover123',
      type: 'track'
    }
  })

  dataStore.reports.set('report-2', {
    reportId: 'report-2',
    reporterId: 'user-1',
    reportedUserId: 'user-2',
    contentType: 'user',
    reason: 'harassment',
    description: 'User is harassing others in comments',
    status: 'pending',
    priority: 'high',
    reportedAt: new Date().toISOString(),
    reporter: { username: 'musiclover123', email: '<EMAIL>' },
    target: {
      id: 'user-2',
      username: 'baduser',
      email: '<EMAIL>',
      type: 'user'
    }
  })

  console.log('✅ Sample data initialized')
}

// Helper functions
const calculatePriority = (reason) => {
  const highPriorityReasons = ['copyright', 'hate_speech', 'violence', 'harassment']
  const mediumPriorityReasons = ['inappropriate', 'misinformation', 'inappropriate_profile', 'fake_account']
  
  if (highPriorityReasons.includes(reason)) return 'high'
  if (mediumPriorityReasons.includes(reason)) return 'medium'
  return 'low'
}

const getStats = () => {
  const totalReports = dataStore.reports.size
  const pendingReports = Array.from(dataStore.reports.values()).filter(r => r.status === 'pending').length
  const resolvedReports = totalReports - pendingReports
  
  return {
    totalReports,
    pendingReports,
    resolvedReports,
    totalUsers: dataStore.users.size,
    totalTracks: dataStore.tracks.size,
    activeUsers: Math.floor(dataStore.users.size * 0.7) // Simulate 70% active
  }
}

// API Routes

// Health check
app.get('/health', (req, res) => {
  console.log('📡 Health check requested')
  res.json({
    status: 'healthy',
    message: 'Enhanced Local Backend running',
    timestamp: new Date().toISOString(),
    version: '2.0.0',
    features: ['admin-panel', 'reporting', 'moderation', 'real-lambda-logic']
  })
})

// User Authentication (simplified)
app.post('/dev/auth/login', (req, res) => {
  const { email, password } = req.body
  console.log(`🔐 Login attempt: ${email}`)
  
  const user = Array.from(dataStore.users.values()).find(u => u.email === email)
  if (user) {
    res.json({
      success: true,
      data: {
        user: {
          userId: user.userId,
          email: user.email,
          username: user.username,
          isAdmin: user.isAdmin
        },
        tokens: {
          accessToken: 'mock-access-token',
          idToken: 'mock-id-token',
          refreshToken: 'mock-refresh-token'
        }
      }
    })
  } else {
    res.status(401).json({ success: false, error: { message: 'Invalid credentials' } })
  }
})

// Get current user
app.get('/dev/auth/me', (req, res) => {
  console.log('👤 Get current user')
  const adminUser = dataStore.users.get('admin-user-1')
  res.json({
    success: true,
    data: adminUser
  })
})

// List tracks
app.get('/dev/tracks', (req, res) => {
  console.log('🎵 List tracks requested')
  const tracks = Array.from(dataStore.tracks.values())
  res.json({
    success: true,
    data: {
      tracks,
      pagination: {
        hasMore: false,
        nextToken: null,
        count: tracks.length
      }
    }
  })
})

// Report Content (matches Lambda function)
app.post('/dev/reports/content', (req, res) => {
  try {
    const { contentId, contentType, reason, description } = req.body
    console.log(`📝 Content report: ${contentId} for ${reason}`)
    
    if (!contentId || !contentType || !reason) {
      return res.status(400).json({
        success: false,
        error: { message: 'Missing required fields: contentId, contentType, reason' }
      })
    }

    const reportId = uuidv4()
    const reportedAt = new Date().toISOString()
    
    const newReport = {
      reportId,
      reporterId: 'current-user-id',
      contentId,
      contentType,
      reason,
      description: description || '',
      status: 'pending',
      priority: calculatePriority(reason),
      reportedAt,
      reporter: { username: 'currentuser', email: '<EMAIL>' },
      target: {
        id: contentId,
        title: dataStore.tracks.get(contentId)?.title || 'Unknown Content',
        artist: dataStore.tracks.get(contentId)?.creator?.username || 'Unknown Artist',
        type: contentType
      }
    }

    dataStore.reports.set(reportId, newReport)
    console.log(`✅ Report created: ${reportId}`)
    
    res.status(201).json({
      success: true,
      data: {
        message: 'Content reported successfully',
        reportId,
        status: 'pending'
      }
    })
  } catch (error) {
    console.error('❌ Error creating content report:', error)
    res.status(500).json({ success: false, error: { message: 'Internal server error' } })
  }
})

// Report User (matches Lambda function)
app.post('/dev/reports/user', (req, res) => {
  try {
    const { reportedUserId, reason, description } = req.body
    console.log(`📝 User report: ${reportedUserId} for ${reason}`)
    
    if (!reportedUserId || !reason) {
      return res.status(400).json({
        success: false,
        error: { message: 'Missing required fields: reportedUserId, reason' }
      })
    }

    const reportId = uuidv4()
    const reportedAt = new Date().toISOString()
    
    const newReport = {
      reportId,
      reporterId: 'current-user-id',
      reportedUserId,
      contentType: 'user',
      reason,
      description: description || '',
      status: 'pending',
      priority: calculatePriority(reason),
      reportedAt,
      reporter: { username: 'currentuser', email: '<EMAIL>' },
      target: {
        id: reportedUserId,
        username: dataStore.users.get(reportedUserId)?.username || 'Unknown User',
        email: dataStore.users.get(reportedUserId)?.email || '<EMAIL>',
        type: 'user'
      }
    }

    dataStore.reports.set(reportId, newReport)
    console.log(`✅ User report created: ${reportId}`)
    
    res.status(201).json({
      success: true,
      data: {
        message: 'User reported successfully',
        reportId,
        status: 'pending'
      }
    })
  } catch (error) {
    console.error('❌ Error creating user report:', error)
    res.status(500).json({ success: false, error: { message: 'Internal server error' } })
  }
})

// Get Reports Queue (Admin only - matches Lambda function)
app.get('/dev/admin/reports', (req, res) => {
  try {
    const { status = 'pending', priority, limit = 50 } = req.query
    console.log(`📊 Admin reports queue: status=${status}, priority=${priority}`)
    
    let reports = Array.from(dataStore.reports.values())
    
    // Filter by status
    if (status && status !== 'all') {
      reports = reports.filter(report => report.status === status)
    }
    
    // Filter by priority
    if (priority && priority !== 'all') {
      reports = reports.filter(report => report.priority === priority)
    }

    // Sort by most recent first
    reports.sort((a, b) => new Date(b.reportedAt) - new Date(a.reportedAt))

    // Limit results
    reports = reports.slice(0, parseInt(limit))

    res.json({
      success: true,
      data: {
        reports,
        pagination: {
          hasMore: false,
          lastEvaluatedKey: null,
          count: reports.length
        },
        filters: { status, priority: priority || 'all' }
      }
    })
  } catch (error) {
    console.error('❌ Error fetching reports:', error)
    res.status(500).json({ success: false, error: { message: 'Internal server error' } })
  }
})

// Take Moderation Action (Admin only - matches Lambda function)
app.post('/dev/admin/moderation/action', (req, res) => {
  try {
    const { reportId, action } = req.body
    console.log(`⚖️ Moderation action: ${action} on ${reportId}`)
    
    if (!reportId || !action) {
      return res.status(400).json({
        success: false,
        error: { message: 'Missing required fields: reportId, action' }
      })
    }

    const report = dataStore.reports.get(reportId)
    if (!report) {
      return res.status(404).json({
        success: false,
        error: { message: 'Report not found' }
      })
    }

    // Update report
    report.status = 'resolved'
    report.action = action
    report.resolvedAt = new Date().toISOString()
    report.updatedAt = new Date().toISOString()
    
    dataStore.reports.set(reportId, report)
    console.log(`✅ Action completed: ${action} on ${reportId}`)

    res.json({
      success: true,
      data: {
        message: `Action ${action} completed successfully`,
        reportId,
        action,
        status: 'resolved'
      }
    })
  } catch (error) {
    console.error('❌ Error taking moderation action:', error)
    res.status(500).json({ success: false, error: { message: 'Internal server error' } })
  }
})

// Get Platform Stats (Admin only - matches Lambda function)
app.get('/dev/admin/stats', (req, res) => {
  try {
    console.log('📈 Admin platform stats requested')
    const stats = getStats()
    res.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('❌ Error fetching stats:', error)
    res.status(500).json({ success: false, error: { message: 'Internal server error' } })
  }
})

// Start server
const server = app.listen(PORT, () => {
  console.log(`🚀 Enhanced Local Backend running on http://localhost:${PORT}`)
  console.log('📊 Available endpoints:')
  console.log('   POST /dev/auth/login - User login')
  console.log('   GET  /dev/auth/me - Get current user')
  console.log('   GET  /dev/tracks - List tracks')
  console.log('   POST /dev/reports/content - Report content')
  console.log('   POST /dev/reports/user - Report user')
  console.log('   GET  /dev/admin/reports - Get reports queue (Admin)')
  console.log('   POST /dev/admin/moderation/action - Take moderation action (Admin)')
  console.log('   GET  /dev/admin/stats - Get platform statistics (Admin)')
  console.log('   GET  /health - Health check')
  console.log('\n✅ Backend ready for admin panel integration!')
  
  // Initialize sample data
  initializeData()
})

server.on('error', (err) => {
  if (err.code === 'EADDRINUSE') {
    console.error(`❌ Port ${PORT} is already in use`)
    process.exit(1)
  } else {
    console.error('❌ Server error:', err)
  }
})

process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...')
  server.close(() => {
    console.log('✅ Server closed')
    process.exit(0)
  })
})
