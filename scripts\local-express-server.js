#!/usr/bin/env node

/**
 * Simple Express Server for Local Development
 * This server simulates the AWS Lambda functions for local testing
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const multer = require('multer');

// Import our services
const CognitoService = require(path.join(__dirname, '../backend/src/services/cognitoService'));
const DynamoService = require(path.join(__dirname, '../backend/src/services/dynamoService'));

const app = express();
const PORT = 3001;

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    // Use the fileKey from the URL params
    const fileKey = decodeURIComponent(req.params.fileKey);
    const fileName = fileKey.split('/').pop(); // Get just the filename part
    cb(null, fileName);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB limit
  },
  fileFilter: function (req, file, cb) {
    // Check if file is audio
    if (file.mimetype.startsWith('audio/')) {
      cb(null, true);
    } else {
      cb(new Error('Only audio files are allowed!'), false);
    }
  }
});

// Set environment variables for local development
process.env.ENVIRONMENT = 'local';
process.env.AWS_REGION = 'us-east-1';
process.env.USER_POOL_ID = 'local_user_pool';
process.env.USER_POOL_CLIENT_ID = 'local_client_id';
process.env.USERS_TABLE_NAME = 'TunamiUsers-local';
process.env.TRACKS_TABLE_NAME = 'TunamiTracks-local';
process.env.LIKES_TABLE_NAME = 'TunamiLikes-local';
process.env.COMMENTS_TABLE_NAME = 'TunamiComments-local';
process.env.FOLLOWS_TABLE_NAME = 'TunamiFollows-local';
process.env.SUBSCRIPTIONS_TABLE_NAME = 'TunamiSubscriptions-local';
process.env.TRANSACTIONS_TABLE_NAME = 'TunamiTransactions-local';
process.env.AUDIO_BUCKET_NAME = 'tunami-audio-files-local';
process.env.STRIPE_SECRET_KEY = 'sk_test_placeholder';
process.env.STRIPE_WEBHOOK_SECRET = 'whsec_placeholder';

// Initialize services
const cognitoService = new CognitoService();
const dynamoService = new DynamoService();

// Middleware
app.use(cors());
app.use(express.json());

// Logging middleware
app.use((req, res, next) => {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.path}`);
  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    environment: 'local',
    timestamp: new Date().toISOString()
  });
});

// Serve audio files
app.get('/audio/:fileKey(*)', (req, res) => {
  try {
    const fileKey = decodeURIComponent(req.params.fileKey);
    const fileName = fileKey.split('/').pop();
    const filePath = path.join(uploadsDir, fileName);

    console.log(`🎵 Serving audio file: ${fileName}`);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      // For demo purposes, serve a sample audio file or return 404
      console.log(`❌ Audio file not found: ${filePath}`);
      return res.status(404).json({
        success: false,
        error: {
          message: 'Audio file not found',
          code: 'FILE_NOT_FOUND'
        }
      });
    }

    // Set appropriate headers for audio streaming
    const stat = fs.statSync(filePath);
    const fileSize = stat.size;
    const range = req.headers.range;

    if (range) {
      // Handle range requests for audio streaming
      const parts = range.replace(/bytes=/, "").split("-");
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
      const chunksize = (end - start) + 1;
      const file = fs.createReadStream(filePath, { start, end });
      const head = {
        'Content-Range': `bytes ${start}-${end}/${fileSize}`,
        'Accept-Ranges': 'bytes',
        'Content-Length': chunksize,
        'Content-Type': 'audio/mpeg',
      };
      res.writeHead(206, head);
      file.pipe(res);
    } else {
      // Serve the entire file
      const head = {
        'Content-Length': fileSize,
        'Content-Type': 'audio/mpeg',
        'Accept-Ranges': 'bytes',
      };
      res.writeHead(200, head);
      fs.createReadStream(filePath).pipe(res);
    }
  } catch (error) {
    console.error('Audio serving error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to serve audio file',
        code: 'AUDIO_SERVE_ERROR'
      }
    });
  }
});

// User registration endpoint
app.post('/users/register', async (req, res) => {
  try {
    const { username, email, password } = req.body;

    if (!username || !email || !password) {
      return res.status(400).json({
        error: 'Missing required fields: username, email, password'
      });
    }

    // Create user in Cognito (mock)
    const cognitoUser = await cognitoService.createUser(username, email, password);

    // Create user in DynamoDB (mock)
    const dynamoUser = await dynamoService.createUser({
      userId: cognitoUser.userId,
      username: cognitoUser.username,
      email: cognitoUser.email
    });

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: {
          userId: dynamoUser.userId,
          username: dynamoUser.username,
          email: dynamoUser.email
        }
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(400).json({
      success: false,
      error: {
        message: error.message,
        code: 'REGISTRATION_ERROR'
      }
    });
  }
});

// User login endpoint
app.post('/users/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        error: 'Missing required fields: email, password'
      });
    }

    // Authenticate user
    const authResult = await cognitoService.authenticateUser(email, password);

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: authResult.user,
        tokens: {
          accessToken: authResult.accessToken,
          idToken: authResult.idToken,
          refreshToken: authResult.refreshToken
        }
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(401).json({
      success: false,
      error: {
        message: error.message,
        code: 'LOGIN_ERROR'
      }
    });
  }
});

// Get current user endpoint
app.get('/users/me', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Missing or invalid authorization header'
      });
    }

    const token = authHeader.substring(7);
    const user = await cognitoService.getUserFromToken(token);

    res.json({
      success: true,
      data: user
    });

  } catch (error) {
    console.error('Get user error:', error);
    res.status(401).json({
      success: false,
      error: {
        message: error.message,
        code: 'AUTHENTICATION_ERROR'
      }
    });
  }
});

// Enhanced tracks listing endpoint
app.get('/tracks', (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      creatorId,
      genre,
      search,
      sortBy = 'uploadDate',
      sortOrder = 'desc'
    } = req.query;

    // Initialize with sample tracks if no local tracks exist
    if (!global.localTracks) {
      global.localTracks = [
        {
          trackId: 'sample-1',
          title: 'AI Symphony No. 1',
          creatorId: 'local-user',
          genre: 'Classical',
          description: 'A beautiful AI-generated classical piece',
          aiToolsUsed: ['AIVA', 'MuseNet'],
          audioFileUrl: 'http://localhost:3001/audio/sample-1.wav',
          fileKey: 'tracks/sample/sample-1.mp3',
          coverImageUrl: '',
          uploadDate: '2024-01-15T10:00:00Z',
          isPublic: true,
          tags: ['classical', 'ai', 'symphony'],
          listenCount: 42,
          likeCount: 15,
          commentCount: 5,
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-15T10:00:00Z'
        },
        {
          trackId: 'sample-2',
          title: 'Electronic Dreams',
          creatorId: 'local-user',
          genre: 'Electronic',
          description: 'Ambient electronic music created with AI',
          aiToolsUsed: ['Amper Music', 'Soundraw'],
          audioFileUrl: 'http://localhost:3001/audio/sample-2.wav',
          fileKey: 'tracks/sample/sample-2.mp3',
          coverImageUrl: '',
          uploadDate: '2024-01-14T15:30:00Z',
          isPublic: true,
          tags: ['electronic', 'ambient', 'ai'],
          listenCount: 28,
          likeCount: 8,
          commentCount: 3,
          createdAt: '2024-01-14T15:30:00Z',
          updatedAt: '2024-01-14T15:30:00Z'
        }
      ];
    }

    let tracks = [...global.localTracks];

    // Apply filters
    if (creatorId) {
      tracks = tracks.filter(track => track.creatorId === creatorId);
    }

    if (genre) {
      tracks = tracks.filter(track =>
        track.genre.toLowerCase() === genre.toLowerCase()
      );
    }

    if (search) {
      const searchLower = search.toLowerCase();
      tracks = tracks.filter(track =>
        track.title.toLowerCase().includes(searchLower) ||
        track.description.toLowerCase().includes(searchLower) ||
        track.genre.toLowerCase().includes(searchLower) ||
        track.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    // Apply sorting
    tracks.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];

      if (sortBy === 'uploadDate' || sortBy === 'createdAt') {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      }

      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : -1;
      } else {
        return aValue > bValue ? 1 : -1;
      }
    });

    // Apply pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    const paginatedTracks = tracks.slice(startIndex, endIndex);

    // Format tracks for response
    const formattedTracks = paginatedTracks.map(track => ({
      trackId: track.trackId,
      title: track.title,
      genre: track.genre,
      description: track.description,
      aiToolsUsed: track.aiToolsUsed || [],
      audioFileUrl: track.audioFileUrl,
      coverImageUrl: track.coverImageUrl,
      uploadDate: track.uploadDate,
      isPublic: track.isPublic,
      tags: track.tags || [],
      listenCount: track.listenCount || 0,
      likeCount: track.likeCount || 0,
      commentCount: track.commentCount || 0,
      creatorId: track.creatorId,
      creator: {
        userId: track.creatorId,
        username: 'LocalUser', // Mock username for local dev
        profileImageUrl: ''
      },
      isOwner: track.creatorId === 'local-user' // Simplified for local dev
    }));

    res.json({
      success: true,
      message: 'Tracks retrieved successfully',
      data: {
        tracks: formattedTracks,
        pagination: {
          limit: limitNum,
          nextToken: endIndex < tracks.length ? `page-${pageNum + 1}` : null,
          hasMore: endIndex < tracks.length,
          totalReturned: formattedTracks.length
        },
        filters: {
          search: search || null,
          genre: genre || null,
          tags: [],
          sortBy: sortBy || 'uploadDate'
        }
      }
    });
  } catch (error) {
    console.error('Tracks listing error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to retrieve tracks',
        code: 'RETRIEVAL_ERROR'
      }
    });
  }
});

// Enhanced track details endpoint
app.get('/tracks/:trackId', (req, res) => {
  try {
    const { trackId } = req.params;

    // Initialize sample tracks if needed
    if (!global.localTracks) {
      global.localTracks = [];
    }

    // Find the track
    const track = global.localTracks.find(t => t.trackId === trackId);

    if (!track) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Track not found',
          code: 'TRACK_NOT_FOUND'
        }
      });
    }

    // Format track for response
    const formattedTrack = {
      trackId: track.trackId,
      title: track.title,
      genre: track.genre,
      description: track.description,
      aiToolsUsed: track.aiToolsUsed || [],
      audioFileUrl: track.audioFileUrl,
      coverImageUrl: track.coverImageUrl,
      uploadDate: track.uploadDate,
      isPublic: track.isPublic,
      tags: track.tags || [],
      listenCount: track.listenCount || 0,
      likeCount: track.likeCount || 0,
      commentCount: track.commentCount || 0,
      creatorId: track.creatorId,
      creator: {
        userId: track.creatorId,
        username: 'LocalUser',
        profileImageUrl: ''
      },
      isOwner: track.creatorId === 'local-user',
      createdAt: track.createdAt,
      updatedAt: track.updatedAt
    };

    res.json({
      success: true,
      message: 'Track details retrieved successfully',
      data: formattedTrack
    });
  } catch (error) {
    console.error('Track details error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to retrieve track details',
        code: 'RETRIEVAL_ERROR'
      }
    });
  }
});

// Enhanced upload endpoint - simulates S3 presigned URL generation
app.post('/tracks/upload', (req, res) => {
  try {
    const { fileName, fileSize, contentType } = req.body;

    // Validate input
    if (!fileName || !fileSize || !contentType) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Missing required fields: fileName, fileSize, contentType',
          code: 'VALIDATION_ERROR'
        }
      });
    }

    // Validate file type
    if (!contentType.includes('audio')) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Invalid file type. Only audio files are allowed.',
          code: 'INVALID_FILE_TYPE'
        }
      });
    }

    // Validate file size (50MB max)
    if (fileSize > 50 * 1024 * 1024) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'File size exceeds 50MB limit',
          code: 'FILE_TOO_LARGE'
        }
      });
    }

    // Generate unique file identifiers
    const fileKey = `tracks/local-user/${Date.now()}-${fileName}`;
    const uploadUrl = `http://localhost:3001/upload-file/${encodeURIComponent(fileKey)}`;
    const s3Url = `http://localhost:3001/audio/${encodeURIComponent(fileKey)}`;

    res.json({
      success: true,
      message: 'Presigned URL generated successfully',
      data: {
        uploadUrl,
        fileKey,
        s3Url,
        expiresIn: 900 // 15 minutes
      }
    });
  } catch (error) {
    console.error('Upload endpoint error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to generate upload URL',
        code: 'UPLOAD_ERROR'
      }
    });
  }
});

// File upload endpoint - handles actual file uploads
app.put('/upload-file/:fileKey(*)', upload.single('file'), (req, res) => {
  try {
    const { fileKey } = req.params;
    const decodedFileKey = decodeURIComponent(fileKey);

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'No file uploaded',
          code: 'NO_FILE'
        }
      });
    }

    console.log(`📁 File uploaded successfully: ${decodedFileKey}`);
    console.log(`📂 Saved to: ${req.file.path}`);
    console.log(`📊 File size: ${req.file.size} bytes`);

    res.status(200).json({
      success: true,
      message: 'File uploaded successfully',
      data: {
        fileKey: decodedFileKey,
        fileName: req.file.filename,
        fileSize: req.file.size,
        mimeType: req.file.mimetype
      }
    });
  } catch (error) {
    console.error('File upload error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'File upload failed',
        code: 'UPLOAD_ERROR'
      }
    });
  }
});

// Track metadata creation endpoint
app.post('/tracks/metadata', (req, res) => {
  try {
    const {
      title,
      genre,
      description,
      aiToolsUsed,
      audioFileUrl,
      fileKey,
      coverImageUrl,
      isPublic,
      tags
    } = req.body;

    // Validate required fields
    if (!title || !genre || !audioFileUrl || !fileKey) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Missing required fields: title, genre, audioFileUrl, fileKey',
          code: 'VALIDATION_ERROR'
        }
      });
    }

    // Generate track data
    const trackId = `track-${Date.now()}`;
    const uploadDate = new Date().toISOString();

    const trackData = {
      trackId,
      creatorId: 'local-user',
      title,
      genre,
      description: description || '',
      aiToolsUsed: aiToolsUsed || [],
      audioFileUrl,
      fileKey,
      coverImageUrl: coverImageUrl || '',
      uploadDate,
      isPublic: isPublic !== false,
      tags: tags || [],
      listenCount: 0,
      likeCount: 0,
      commentCount: 0,
      createdAt: uploadDate,
      updatedAt: uploadDate
    };

    // Store in memory (in real app, this would go to DynamoDB)
    if (!global.localTracks) {
      global.localTracks = [];
    }
    global.localTracks.unshift(trackData); // Add to beginning

    console.log(`🎵 Track metadata created: ${title} by local-user`);

    res.json({
      success: true,
      message: 'Track metadata created successfully',
      data: {
        track: {
          ...trackData,
          creator: {
            userId: 'local-user',
            username: 'LocalUser',
            profileImageUrl: ''
          }
        }
      }
    });
  } catch (error) {
    console.error('Metadata creation error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to create track metadata',
        code: 'METADATA_ERROR'
      }
    });
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: error.message
  });
});

// Social Features Endpoints

// Like/Unlike Track
app.post('/tracks/:trackId/like', async (req, res) => {
  try {
    const { trackId } = req.params;
    const { action } = req.body; // 'like' or 'unlike'
    const userId = 'local-user'; // Mock user for local dev

    console.log(`🎵 ${action || 'toggle'} track: ${trackId} by user: ${userId}`);

    // Find track
    const track = tracks.find(t => t.trackId === trackId);
    if (!track) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Track not found',
          code: 'TRACK_NOT_FOUND'
        }
      });
    }

    // Mock like status (in real app, this would check DynamoDB)
    const isCurrentlyLiked = Math.random() > 0.5; // Random for demo
    const shouldLike = action === 'like' || (action === undefined && !isCurrentlyLiked);

    // Update like count
    if (shouldLike && !isCurrentlyLiked) {
      track.likeCount = (track.likeCount || 0) + 1;
    } else if (!shouldLike && isCurrentlyLiked) {
      track.likeCount = Math.max((track.likeCount || 0) - 1, 0);
    }

    res.json({
      success: true,
      message: shouldLike ? 'Track liked successfully' : 'Track unliked successfully',
      data: {
        trackId,
        isLiked: shouldLike,
        likeCount: track.likeCount,
        userId,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Like track error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to process like action',
        code: 'LIKE_ERROR'
      }
    });
  }
});

// Add Comment to Track
app.post('/tracks/:trackId/comments', async (req, res) => {
  try {
    const { trackId } = req.params;
    const { content } = req.body;
    const userId = 'local-user'; // Mock user for local dev

    console.log(`💬 Adding comment to track: ${trackId}`);

    // Validate input
    if (!content || content.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Comment content is required',
          code: 'VALIDATION_ERROR'
        }
      });
    }

    // Find track
    const track = tracks.find(t => t.trackId === trackId);
    if (!track) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Track not found',
          code: 'TRACK_NOT_FOUND'
        }
      });
    }

    // Create comment
    const commentId = `comment-${Date.now()}`;
    const createdAt = new Date().toISOString();

    const comment = {
      commentId,
      trackId,
      userId,
      content: content.trim(),
      createdAt,
      user: {
        userId,
        username: 'LocalUser',
        profileImageUrl: ''
      }
    };

    // Update comment count
    track.commentCount = (track.commentCount || 0) + 1;

    res.json({
      success: true,
      message: 'Comment added successfully',
      data: comment
    });

  } catch (error) {
    console.error('Add comment error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to add comment',
        code: 'COMMENT_ERROR'
      }
    });
  }
});

// Get Track Comments
app.get('/tracks/:trackId/comments', async (req, res) => {
  try {
    const { trackId } = req.params;
    const limit = parseInt(req.query.limit) || 20;

    console.log(`💬 Getting comments for track: ${trackId}`);

    // Find track
    const track = tracks.find(t => t.trackId === trackId);
    if (!track) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Track not found',
          code: 'TRACK_NOT_FOUND'
        }
      });
    }

    // Mock comments (in real app, this would query DynamoDB)
    const mockComments = [
      {
        commentId: 'comment-1',
        trackId,
        userId: 'user-1',
        content: 'Amazing AI-generated track! Love the creativity.',
        createdAt: '2024-01-15T12:00:00Z',
        user: {
          userId: 'user-1',
          username: 'MusicLover',
          profileImageUrl: ''
        }
      },
      {
        commentId: 'comment-2',
        trackId,
        userId: 'user-2',
        content: 'The AI tools used here are incredible. What a time to be alive!',
        createdAt: '2024-01-15T13:30:00Z',
        user: {
          userId: 'user-2',
          username: 'TechEnthusiast',
          profileImageUrl: ''
        }
      }
    ].slice(0, limit);

    res.json({
      success: true,
      message: 'Comments retrieved successfully',
      data: {
        comments: mockComments,
        pagination: {
          limit,
          hasMore: false,
          totalReturned: mockComments.length
        }
      }
    });

  } catch (error) {
    console.error('Get comments error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get comments',
        code: 'COMMENTS_ERROR'
      }
    });
  }
});

// Monetization Features Endpoints

// Create Checkout Session for Subscription
app.post('/payments/checkout', async (req, res) => {
  try {
    const { planId, successUrl, cancelUrl } = req.body;
    const userId = 'local-user'; // Mock user for local dev

    console.log(`💳 Creating checkout session for plan: ${planId} by user: ${userId}`);

    // Validate required fields
    if (!planId || !successUrl || !cancelUrl) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Missing required fields: planId, successUrl, cancelUrl',
          code: 'VALIDATION_ERROR'
        }
      });
    }

    // Validate plan ID
    if (planId !== 'tunami-supporter') {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Invalid plan ID',
          code: 'INVALID_PLAN'
        }
      });
    }

    // Mock checkout session creation
    const sessionId = `cs_mock_${Date.now()}`;
    const checkoutUrl = `${successUrl}?session_id=${sessionId}&success=true`;

    res.json({
      success: true,
      message: 'Checkout session created successfully',
      data: {
        sessionId,
        checkoutUrl,
        customerId: 'cus_mock_customer',
        subscriptionId: `sub_mock_${Date.now()}`
      }
    });

  } catch (error) {
    console.error('Create checkout session error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to create checkout session',
        code: 'CHECKOUT_ERROR'
      }
    });
  }
});

// Create Tipping Payment Intent
app.post('/payments/tip', async (req, res) => {
  try {
    const { toUserId, trackId, amount, currency = 'usd', message = '' } = req.body;
    const fromUserId = 'local-user'; // Mock user for local dev

    console.log(`💰 Creating tip payment: $${amount/100} from ${fromUserId} to ${toUserId} for track ${trackId}`);

    // Validate required fields
    if (!toUserId || !trackId || !amount) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Missing required fields: toUserId, trackId, amount',
          code: 'VALIDATION_ERROR'
        }
      });
    }

    // Validate amount ($1 to $100)
    if (amount < 100 || amount > 10000) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Amount must be between $1 and $100',
          code: 'INVALID_AMOUNT'
        }
      });
    }

    // Validate that user is not tipping themselves
    if (fromUserId === toUserId) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Cannot tip yourself',
          code: 'SELF_TIP_ERROR'
        }
      });
    }

    // Find track to verify it exists and get details
    const track = global.localTracks?.find(t => t.trackId === trackId);
    if (!track) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Track not found',
          code: 'TRACK_NOT_FOUND'
        }
      });
    }

    // Calculate platform fee (10%)
    const platformFee = Math.floor(amount * 0.1);
    const creatorAmount = amount - platformFee;

    // Mock payment intent creation
    const paymentIntentId = `pi_mock_${Date.now()}`;
    const clientSecret = `${paymentIntentId}_secret_mock`;

    res.json({
      success: true,
      message: 'Payment intent created successfully',
      data: {
        paymentIntentId,
        clientSecret,
        amount,
        currency,
        platformFee,
        creatorAmount,
        recipient: {
          userId: toUserId,
          username: 'LocalCreator'
        },
        track: {
          trackId: trackId,
          title: track.title
        }
      }
    });

  } catch (error) {
    console.error('Create tipping payment intent error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to create payment intent',
        code: 'PAYMENT_INTENT_ERROR'
      }
    });
  }
});

// Stripe Webhook Handler
app.post('/webhooks/stripe', async (req, res) => {
  try {
    console.log('🔔 Stripe webhook received');

    // Mock webhook processing for local development
    const event = {
      id: `evt_mock_${Date.now()}`,
      type: 'payment_intent.succeeded',
      data: {
        object: {
          id: 'pi_mock_payment',
          amount: 500,
          currency: 'usd',
          metadata: {
            type: 'tip',
            fromUserId: 'local-user',
            toUserId: 'creator-user',
            trackId: 'sample-1'
          }
        }
      }
    };

    console.log(`Processing webhook event: ${event.type}`);

    // In a real implementation, this would process the webhook
    // and update the database accordingly

    res.json({
      received: true,
      eventType: event.type,
      eventId: event.id
    });

  } catch (error) {
    console.error('Webhook processing error:', error);
    res.status(400).json({
      error: 'Webhook processing failed',
      message: error.message
    });
  }
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    path: req.path
  });
});

// Start server with robust error handling
const server = app.listen(PORT, () => {
  console.log('🚀 Tunami MVP Local Development Server');
  console.log('');
  console.log(`✅ Server running on http://localhost:${PORT}`);
  console.log('✅ Environment: local (mock services)');
  console.log('');
  console.log('📋 Available endpoints:');
  console.log('• GET  /health');
  console.log('• POST /users/register');
  console.log('• POST /users/login');
  console.log('• GET  /users/me');
  console.log('• GET  /tracks (with filtering & pagination)');
  console.log('• GET  /tracks/:trackId');
  console.log('• POST /tracks/upload (presigned URL generation)');
  console.log('• PUT  /upload-file/:fileKey (file upload simulation)');
  console.log('• POST /tracks/metadata (track metadata creation)');
  console.log('• POST /tracks/:trackId/like (like/unlike tracks)');
  console.log('• POST /tracks/:trackId/comments (add comments)');
  console.log('• GET  /tracks/:trackId/comments (get comments)');
  console.log('• POST /payments/checkout (create subscription checkout)');
  console.log('• POST /payments/tip (create tipping payment intent)');
  console.log('• POST /webhooks/stripe (Stripe webhook handler)');
  console.log('');
  console.log('💡 Test users available:');
  console.log('• <EMAIL> / TestPass123!');
  console.log('• <EMAIL> / TestPass123!');
  console.log('• <EMAIL> / AdminPass123!');
  console.log('');
  console.log('🎯 Frontend should connect to this server automatically');
});

// Handle server errors
server.on('error', (error) => {
  if (error.code === 'EADDRINUSE') {
    console.error(`❌ Port ${PORT} is already in use`);
    process.exit(1);
  } else {
    console.error(`❌ Server error: ${error.message}`);
    process.exit(1);
  }
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  server.close(() => {
    console.log('✅ Server shut down gracefully');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down server...');
  server.close(() => {
    console.log('✅ Server shut down gracefully');
    process.exit(0);
  });
});
