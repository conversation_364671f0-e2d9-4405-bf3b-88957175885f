{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["cloudformation:CreateStack", "cloudformation:UpdateStack", "cloudformation:DeleteStack", "cloudformation:DescribeStacks", "cloudformation:DescribeStackEvents", "cloudformation:DescribeStackResources", "cloudformation:GetTemplate", "cloudformation:ValidateTemplate", "cloudformation:ListStacks"], "Resource": ["arn:aws:cloudformation:us-east-1:600865702437:stack/tunami-mvp-*/*"]}, {"Effect": "Allow", "Action": ["lambda:CreateFunction", "lambda:UpdateFunctionCode", "lambda:UpdateFunctionConfiguration", "lambda:DeleteFunction", "lambda:GetFunction", "lambda:ListFunctions", "lambda:AddPermission", "lambda:RemovePermission", "lambda:GetPolicy", "lambda:PutProvisionedConcurrencyConfig", "lambda:DeleteProvisionedConcurrencyConfig", "lambda:GetProvisionedConcurrencyConfig", "lambda:ListProvisionedConcurrencyConfigs"], "Resource": ["arn:aws:lambda:us-east-1:600865702437:function:tunami-*"]}, {"Effect": "Allow", "Action": ["apigateway:GET", "apigateway:POST", "apigateway:PUT", "apigateway:DELETE", "apigateway:PATCH"], "Resource": ["arn:aws:apigateway:us-east-1::/restapis*", "arn:aws:apigateway:us-east-1::/apis*"]}, {"Effect": "Allow", "Action": ["dynamodb:CreateTable", "dynamodb:UpdateTable", "dynamodb:DeleteTable", "dynamodb:DescribeTable", "dynamodb:ListTables", "dynamodb:TagResource", "dynamodb:UntagResource", "dynamodb:ListTagsOfResource"], "Resource": ["arn:aws:dynamodb:us-east-1:600865702437:table/Tunami*"]}, {"Effect": "Allow", "Action": ["cognito-idp:CreateUserPool", "cognito-idp:UpdateUserPool", "cognito-idp:DeleteUserPool", "cognito-idp:DescribeUserPool", "cognito-idp:CreateUserPoolClient", "cognito-idp:UpdateUserPoolClient", "cognito-idp:DeleteUserPoolClient", "cognito-idp:DescribeUserPoolClient", "cognito-idp:ListUserPools", "cognito-idp:ListUserPoolClients"], "Resource": ["arn:aws:cognito-idp:us-east-1:600865702437:userpool/*"]}, {"Effect": "Allow", "Action": ["s3:CreateBucket", "s3:DeleteBucket", "s3:GetBucketLocation", "s3:GetBucketPolicy", "s3:PutBucketPolicy", "s3:DeleteBucketPolicy", "s3:GetBucketWebsite", "s3:PutBucketWebsite", "s3:DeleteBucketWebsite", "s3:GetBucketCORS", "s3:PutBucketCORS", "s3:DeleteBucketCORS", "s3:GetObject", "s3:PutObject", "s3:DeleteObject", "s3:ListBucket", "s3:GetBucketVersioning", "s3:PutBucketVersioning"], "Resource": ["arn:aws:s3:::tunami-*", "arn:aws:s3:::tunami-*/*", "arn:aws:s3:::aws-sam-cli-*", "arn:aws:s3:::aws-sam-cli-*/*"]}, {"Effect": "Allow", "Action": ["cloudfront:CreateDistribution", "cloudfront:UpdateDistribution", "cloudfront:DeleteDistribution", "cloudfront:GetDistribution", "cloudfront:ListDistributions", "cloudfront:CreateInvalidation", "cloudfront:GetInvalidation", "cloudfront:ListInvalidations"], "Resource": "*"}, {"Effect": "Allow", "Action": ["iam:CreateRole", "iam:UpdateRole", "iam:DeleteRole", "iam:GetRole", "iam:PassRole", "iam:AttachRolePolicy", "iam:DetachRolePolicy", "iam:PutRolePolicy", "iam:DeleteRolePolicy", "iam:GetRolePolicy", "iam:ListRolePolicies", "iam:ListAttachedRolePolicies", "iam:TagRole", "iam:UntagRole"], "Resource": ["arn:aws:iam::600865702437:role/tunami-*", "arn:aws:iam::600865702437:role/aws-sam-cli-*"]}, {"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:DeleteLogGroup", "logs:DescribeLogGroups", "logs:PutRetentionPolicy", "logs:DeleteRetentionPolicy"], "Resource": ["arn:aws:logs:us-east-1:600865702437:log-group:/aws/lambda/tunami-*"]}, {"Effect": "Allow", "Action": ["s3:ListAllMyBuckets", "s3:GetBucketLocation"], "Resource": "*"}]}