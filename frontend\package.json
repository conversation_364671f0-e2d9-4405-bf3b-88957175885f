{"name": "tunami-frontend", "version": "1.0.0", "description": "Tunami MVP Frontend - React Application", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx --fix", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"axios": "^1.6.0", "lucide-react": "^0.292.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.18.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "@vitest/ui": "^0.34.6", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "vite": "^4.5.0", "vitest": "^0.34.6"}, "keywords": ["react", "vite", "tailwindcss", "frontend"], "author": "Tunami Team", "license": "MIT"}