<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tunami Admin Panel Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900">Tunami Admin Panel</h1>
                    <span class="ml-2 px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">DEMO</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-700">Admin: <EMAIL></span>
                    <button class="px-3 py-2 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200">Logout</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Server Status -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">🚀 Sprint 5 Admin System Status</h2>
            <div id="serverStatus" class="space-y-2">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span class="text-sm text-gray-700">Backend Server: Attempting to connect...</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span class="text-sm text-gray-700">Admin Components: ✅ Ready</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                    <span class="text-sm text-gray-700">Report System: ✅ Ready</span>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="border-b border-gray-200 mb-8">
            <nav class="-mb-px flex space-x-8">
                <button onclick="showTab('overview')" id="tab-overview" class="tab-button border-b-2 border-blue-500 text-blue-600 py-2 px-1 font-medium text-sm">
                    📊 Overview
                </button>
                <button onclick="showTab('reports')" id="tab-reports" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 py-2 px-1 font-medium text-sm">
                    🚨 Reports Queue
                </button>
                <button onclick="showTab('demo')" id="tab-demo" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 py-2 px-1 font-medium text-sm">
                    🎮 Demo Features
                </button>
            </nav>
        </div>

        <!-- Tab Content -->
        <div id="content-overview" class="tab-content">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">Platform Overview</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="statsGrid">
                <!-- Stats will be loaded here -->
            </div>
        </div>

        <div id="content-reports" class="tab-content hidden">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">Reports Queue</h2>
            <div id="reportsContainer">
                <!-- Reports will be loaded here -->
            </div>
        </div>

        <div id="content-demo" class="tab-content hidden">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">Demo Features</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">🚨 Report Submission</h3>
                    <button onclick="showReportModal()" class="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                        Test Report Modal
                    </button>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">⚖️ Moderation Actions</h3>
                    <div class="space-y-2">
                        <button onclick="testAction('dismiss')" class="w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
                            Test Dismiss Action
                        </button>
                        <button onclick="testAction('warn')" class="w-full px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700">
                            Test Warn User
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Modal -->
    <div id="reportModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-lg max-w-md w-full mx-4">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">🚨 Report Content</h3>
                <form id="reportForm">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Reason</label>
                        <select id="reportReason" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            <option value="inappropriate">Inappropriate content</option>
                            <option value="spam">Spam</option>
                            <option value="copyright">Copyright infringement</option>
                            <option value="harassment">Harassment</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea id="reportDescription" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md"></textarea>
                    </div>
                    <div class="flex space-x-3">
                        <button type="button" onclick="hideReportModal()" class="flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            Cancel
                        </button>
                        <button type="submit" class="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                            Submit Report
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Mock data
        const mockStats = {
            totalReports: 23,
            pendingReports: 8,
            resolvedReports: 15,
            totalUsers: 1247,
            totalTracks: 3891,
            activeUsers: 342
        };

        const mockReports = [
            {
                id: 'report-1',
                reason: 'inappropriate',
                status: 'pending',
                reportedAt: '2024-06-02T10:30:00Z',
                reporter: { username: 'user123', email: '<EMAIL>' },
                target: { id: 'track-1', title: 'AI Symphony No. 1', artist: 'LocalUser', type: 'track' },
                description: 'This track contains inappropriate content',
                priority: 'medium'
            },
            {
                id: 'report-2',
                reason: 'harassment',
                status: 'pending',
                reportedAt: '2024-06-02T09:15:00Z',
                reporter: { username: 'reporter456', email: '<EMAIL>' },
                target: { id: 'user-2', username: 'baduser', email: '<EMAIL>', type: 'user' },
                description: 'User is harassing other members in comments',
                priority: 'high'
            }
        ];

        // Tab management
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => tab.classList.add('hidden'));
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('border-blue-500', 'text-blue-600');
                btn.classList.add('border-transparent', 'text-gray-500');
            });

            // Show selected tab
            document.getElementById(`content-${tabName}`).classList.remove('hidden');
            document.getElementById(`tab-${tabName}`).classList.remove('border-transparent', 'text-gray-500');
            document.getElementById(`tab-${tabName}`).classList.add('border-blue-500', 'text-blue-600');

            // Load content
            if (tabName === 'overview') loadStats();
            if (tabName === 'reports') loadReports();
        }

        // Load statistics
        function loadStats() {
            const statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = `
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-red-100">
                            <span class="text-red-600">🚨</span>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Pending Reports</p>
                            <p class="text-2xl font-semibold text-gray-900">${mockStats.pendingReports}</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100">
                            <span class="text-green-600">✅</span>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Resolved Reports</p>
                            <p class="text-2xl font-semibold text-gray-900">${mockStats.resolvedReports}</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100">
                            <span class="text-blue-600">👥</span>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Users</p>
                            <p class="text-2xl font-semibold text-gray-900">${mockStats.totalUsers}</p>
                        </div>
                    </div>
                </div>
            `;
        }

        // Load reports
        function loadReports() {
            const container = document.getElementById('reportsContainer');
            container.innerHTML = mockReports.map(report => `
                <div class="bg-white rounded-lg shadow p-6 mb-4">
                    <div class="flex justify-between items-start mb-4">
                        <div class="flex items-center space-x-3">
                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-${report.priority === 'high' ? 'red' : 'yellow'}-100 text-${report.priority === 'high' ? 'red' : 'yellow'}-800">
                                ${report.priority} priority
                            </span>
                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                                ${report.status}
                            </span>
                        </div>
                        <span class="text-sm text-gray-500">${new Date(report.reportedAt).toLocaleDateString()}</span>
                    </div>
                    <h3 class="font-medium text-gray-900 mb-2">
                        ${report.target.title || report.target.username}
                    </h3>
                    <p class="text-sm text-gray-600 mb-4">${report.description}</p>
                    <div class="flex space-x-2">
                        <button onclick="takeAction('${report.id}', 'dismiss')" class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">
                            Dismiss
                        </button>
                        <button onclick="takeAction('${report.id}', 'warn')" class="px-3 py-2 text-sm bg-yellow-100 text-yellow-700 rounded-md hover:bg-yellow-200">
                            Warn User
                        </button>
                        <button onclick="takeAction('${report.id}', 'remove')" class="px-3 py-2 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200">
                            Remove Content
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // Modal functions
        function showReportModal() {
            document.getElementById('reportModal').classList.remove('hidden');
        }

        function hideReportModal() {
            document.getElementById('reportModal').classList.add('hidden');
        }

        // Action functions
        function takeAction(reportId, action) {
            alert(`✅ Action "${action}" taken on report ${reportId}`);
            loadReports(); // Refresh reports
        }

        function testAction(action) {
            alert(`✅ Test action "${action}" completed successfully!`);
        }

        // Form submission
        document.getElementById('reportForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const reason = document.getElementById('reportReason').value;
            const description = document.getElementById('reportDescription').value;
            alert(`✅ Report submitted successfully!\nReason: ${reason}\nDescription: ${description}`);
            hideReportModal();
        });

        // Check server status
        async function checkServerStatus() {
            try {
                const response = await fetch('http://localhost:3001/health');
                if (response.ok) {
                    document.getElementById('serverStatus').innerHTML = `
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-sm text-gray-700">Backend Server: ✅ Connected</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                            <span class="text-sm text-gray-700">Admin Components: ✅ Ready</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                            <span class="text-sm text-gray-700">Report System: ✅ Ready</span>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('serverStatus').innerHTML = `
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                        <span class="text-sm text-gray-700">Backend Server: ❌ Not connected (using demo mode)</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                        <span class="text-sm text-gray-700">Admin Components: ✅ Ready</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                        <span class="text-sm text-gray-700">Report System: ✅ Ready (demo mode)</span>
                    </div>
                `;
            }
        }

        // Initialize
        window.onload = function() {
            showTab('overview');
            checkServerStatus();
        };
    </script>
</body>
</html>
