/**
 * Lambda function to create Stripe payment intent for tipping creators
 * Handles secure tip processing with platform fees
 */

const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, GetCommand } = require('@aws-sdk/lib-dynamodb');
const { successResponse, errorResponse } = require('../utils/responseHelper');
const { validateInput } = require('../utils/validation');
const Joi = require('joi');

// Initialize DynamoDB client
const client = new DynamoDBClient({
  region: process.env.AWS_REGION || 'us-east-1'
});
const docClient = DynamoDBDocumentClient.from(client);

// Environment variables
const USERS_TABLE = process.env.USERS_TABLE_NAME || 'TunamiUsers';
const TRACKS_TABLE = process.env.TRACKS_TABLE_NAME || 'TunamiTracks';

// Validation schema for tip request
const tipSchema = Joi.object({
  toUserId: Joi.string().required(),
  trackId: Joi.string().required(),
  amount: Joi.number().integer().min(100).max(10000).required(), // $1 to $100 in cents
  currency: Joi.string().valid('usd').default('usd'),
  message: Joi.string().max(500).allow('').default('')
});

// Mock Stripe for local development
const mockStripe = {
  paymentIntents: {
    create: async (params) => {
      console.log('Mock Stripe payment intent creation:', params);
      return {
        id: 'pi_mock_' + Date.now(),
        client_secret: 'pi_mock_' + Date.now() + '_secret_mock',
        amount: params.amount,
        currency: params.currency,
        status: 'requires_payment_method',
        metadata: params.metadata
      };
    }
  }
};

/**
 * Main handler function
 */
exports.handler = async (event) => {
  console.log('Create tipping payment intent request:', JSON.stringify(event, null, 2));

  try {
    // Extract user ID from Cognito authorizer
    const fromUserId = event.requestContext?.authorizer?.claims?.sub || 'local-user';
    if (!fromUserId) {
      return errorResponse('User not authenticated', 401, 'UNAUTHORIZED');
    }

    // Parse and validate request body
    const body = JSON.parse(event.body || '{}');
    const validation = validateInput(body, tipSchema);
    
    if (!validation.isValid) {
      return errorResponse('Invalid input data', 400, 'VALIDATION_ERROR', validation.errors);
    }

    const { toUserId, trackId, amount, currency, message } = validation.value;

    // Validate that user is not tipping themselves
    if (fromUserId === toUserId) {
      return errorResponse('Cannot tip yourself', 400, 'SELF_TIP_ERROR');
    }

    // Verify recipient user exists
    const recipientUser = await getUser(toUserId);
    if (!recipientUser) {
      return errorResponse('Recipient user not found', 404, 'USER_NOT_FOUND');
    }

    // Verify track exists and belongs to recipient
    const track = await getTrack(trackId);
    if (!track) {
      return errorResponse('Track not found', 404, 'TRACK_NOT_FOUND');
    }

    if (track.creatorId !== toUserId) {
      return errorResponse('Track does not belong to recipient user', 400, 'TRACK_OWNER_MISMATCH');
    }

    // Get sender user info for metadata
    const senderUser = await getUser(fromUserId);

    // Use mock Stripe for local development
    let stripe;
    if (process.env.NODE_ENV === 'development' || !process.env.STRIPE_SECRET_KEY || process.env.STRIPE_SECRET_KEY === 'sk_test_placeholder') {
      console.log('Using mock Stripe for local development');
      stripe = mockStripe;
    } else {
      stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
    }

    // Calculate platform fee (10%)
    const platformFee = Math.floor(amount * 0.1);
    const creatorAmount = amount - platformFee;

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amount,
      currency: currency,
      automatic_payment_methods: {
        enabled: true,
      },
      metadata: {
        type: 'tip',
        fromUserId: fromUserId,
        fromUsername: senderUser?.username || 'Anonymous',
        toUserId: toUserId,
        toUsername: recipientUser.username,
        trackId: trackId,
        trackTitle: track.title,
        message: message,
        platformFee: platformFee.toString(),
        creatorAmount: creatorAmount.toString()
      },
      description: `Tip for "${track.title}" by ${recipientUser.username}`,
      receipt_email: event.requestContext?.authorizer?.claims?.email,
      statement_descriptor: 'TUNAMI TIP'
    });

    console.log('Payment intent created:', paymentIntent.id);

    return successResponse({
      paymentIntentId: paymentIntent.id,
      clientSecret: paymentIntent.client_secret,
      amount: amount,
      currency: currency,
      platformFee: platformFee,
      creatorAmount: creatorAmount,
      recipient: {
        userId: toUserId,
        username: recipientUser.username
      },
      track: {
        trackId: trackId,
        title: track.title
      }
    }, 'Payment intent created successfully');

  } catch (error) {
    console.error('Create tipping payment intent error:', error);

    // Handle specific Stripe errors
    if (error.type === 'StripeCardError') {
      return errorResponse('Card error: ' + error.message, 400, 'CARD_ERROR');
    } else if (error.type === 'StripeRateLimitError') {
      return errorResponse('Rate limit exceeded', 429, 'RATE_LIMIT');
    } else if (error.type === 'StripeInvalidRequestError') {
      return errorResponse('Invalid request: ' + error.message, 400, 'INVALID_REQUEST');
    } else if (error.type === 'StripeAPIError') {
      return errorResponse('Stripe API error', 500, 'STRIPE_API_ERROR');
    } else if (error.type === 'StripeConnectionError') {
      return errorResponse('Network error', 500, 'NETWORK_ERROR');
    } else if (error.type === 'StripeAuthenticationError') {
      return errorResponse('Authentication error', 500, 'AUTH_ERROR');
    }

    return errorResponse('Failed to create payment intent', 500, 'PAYMENT_INTENT_ERROR');
  }
};

/**
 * Get user by ID
 */
async function getUser(userId) {
  try {
    const response = await docClient.send(new GetCommand({
      TableName: USERS_TABLE,
      Key: { userId }
    }));
    return response.Item;
  } catch (error) {
    console.error('Error getting user:', error);
    return null;
  }
}

/**
 * Get track by ID
 */
async function getTrack(trackId) {
  try {
    const response = await docClient.send(new GetCommand({
      TableName: TRACKS_TABLE,
      Key: { trackId }
    }));
    return response.Item;
  } catch (error) {
    console.error('Error getting track:', error);
    return null;
  }
}
