import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext.jsx'
import { LogOut, User, Music, Compass, Upload, Home, Shield } from 'lucide-react'
import SubscriptionButton from './SubscriptionButton'

const Layout = ({ children }) => {
  const { user, logout, isAdmin } = useAuth()
  const location = useLocation()

  const handleLogout = () => {
    logout()
  }

  const navigation = [
    { name: 'Discover', href: '/discover', icon: Compass },
    { name: 'Dashboard', href: '/dashboard', icon: Home },
    { name: 'Creator Studio', href: '/creator', icon: Upload },
    ...(isAdmin() ? [{ name: 'Admin', href: '/admin', icon: Shield }] : []),
  ]

  const isActive = (href) => location.pathname === href

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <Link to="/discover" className="flex items-center">
              <Music className="w-8 h-8 text-blue-500" />
              <h1 className="ml-2 text-2xl font-bold text-gray-900">
                Tunami
              </h1>
              <span className="ml-2 px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                MVP
              </span>
            </Link>

            {/* Navigation */}
            <nav className="hidden md:flex space-x-8">
              {navigation.map((item) => {
                const Icon = item.icon
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      isActive(item.href)
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{item.name}</span>
                  </Link>
                )
              })}
            </nav>

            {/* User menu */}
            <div className="flex items-center space-x-4">
              <SubscriptionButton
                isSubscribed={false} // TODO: Get from user context
                size="sm"
              />

              <div className="flex items-center space-x-2 text-sm text-gray-700">
                <User className="h-4 w-4" />
                <span>Welcome, {user?.username}</span>
              </div>

              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              >
                <LogOut className="h-4 w-4" />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main>
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-auto">
        <div className="container py-6">
          <div className="text-center text-sm text-gray-500">
            © 2024 Tunami MVP. Built with AWS SAM and React.
          </div>
        </div>
      </footer>
    </div>
  )
}

export default Layout
