const { DynamoDBClient, ScanCommand } = require('@aws-sdk/client-dynamodb')
const { unmarshall } = require('@aws-sdk/util-dynamodb')
const { successResponse, errorResponse } = require('../utils/responseHelper')
const { validateInput } = require('../utils/validation')
const Joi = require('joi')

const dynamoClient = new DynamoDBClient({ region: process.env.AWS_REGION })

// Validation schema for search parameters
const searchSchema = Joi.object({
  query: Joi.string().required().min(1).max(100).trim(),
  genre: Joi.string().allow('').max(50),
  aiTool: Joi.string().allow('').max(100),
  limit: Joi.number().integer().min(1).max(50).default(20),
  lastEvaluatedKey: Joi.string().allow('')
})

/**
 * Lambda handler for searching tracks
 */
exports.handler = async (event) => {
  console.log('Search tracks request:', JSON.stringify(event, null, 2))

  try {
    // Parse and validate query parameters
    const queryParams = event.queryStringParameters || {}
    const validation = validateInput(queryParams, searchSchema)
    
    if (!validation.isValid) {
      return errorResponse('Invalid search parameters', 400, 'VALIDATION_ERROR', validation.errors)
    }

    const { query, genre, aiTool, limit, lastEvaluatedKey } = validation.value

    // Build filter expression
    const filterExpressions = []
    const expressionAttributeNames = {}
    const expressionAttributeValues = {}

    // Only search public tracks
    filterExpressions.push('isPublic = :isPublic')
    expressionAttributeValues[':isPublic'] = { S: 'true' }

    // Search in title (case-insensitive)
    if (query) {
      filterExpressions.push('contains(#title, :query)')
      expressionAttributeNames['#title'] = 'title'
      expressionAttributeValues[':query'] = { S: query.toLowerCase() }
    }

    // Filter by genre
    if (genre) {
      filterExpressions.push('#genre = :genre')
      expressionAttributeNames['#genre'] = 'genre'
      expressionAttributeValues[':genre'] = { S: genre }
    }

    // Filter by AI tool
    if (aiTool) {
      filterExpressions.push('contains(aiToolsUsed, :aiTool)')
      expressionAttributeValues[':aiTool'] = { S: aiTool }
    }

    // Build scan command
    const scanCommand = new ScanCommand({
      TableName: process.env.TRACKS_TABLE_NAME,
      FilterExpression: filterExpressions.join(' AND '),
      ExpressionAttributeNames: Object.keys(expressionAttributeNames).length > 0 ? expressionAttributeNames : undefined,
      ExpressionAttributeValues: expressionAttributeValues,
      Limit: limit
    })

    // Add pagination if provided
    if (lastEvaluatedKey) {
      try {
        scanCommand.ExclusiveStartKey = JSON.parse(Buffer.from(lastEvaluatedKey, 'base64').toString())
      } catch (error) {
        return errorResponse('Invalid pagination token', 400, 'INVALID_PAGINATION')
      }
    }

    const result = await dynamoClient.send(scanCommand)

    // Process and score results
    const tracks = (result.Items || []).map(item => {
      const track = unmarshall(item)
      
      // Calculate relevance score
      let score = 0
      const queryLower = query.toLowerCase()
      const titleLower = track.title.toLowerCase()
      
      // Exact match gets highest score
      if (titleLower === queryLower) {
        score += 100
      }
      // Title starts with query
      else if (titleLower.startsWith(queryLower)) {
        score += 50
      }
      // Title contains query
      else if (titleLower.includes(queryLower)) {
        score += 25
      }
      
      // Boost score for genre match
      if (genre && track.genre === genre) {
        score += 10
      }
      
      // Boost score for AI tool match
      if (aiTool && track.aiToolsUsed && track.aiToolsUsed.includes(aiTool)) {
        score += 10
      }
      
      // Boost score based on popularity
      score += Math.min((track.likeCount || 0) * 0.1, 10)
      score += Math.min((track.listenCount || 0) * 0.01, 5)

      return {
        trackId: track.trackId,
        title: track.title,
        genre: track.genre,
        description: track.description,
        aiToolsUsed: track.aiToolsUsed || [],
        audioFileUrl: track.audioFileUrl,
        coverImageUrl: track.coverImageUrl,
        uploadDate: track.uploadDate,
        tags: track.tags || [],
        listenCount: track.listenCount || 0,
        likeCount: track.likeCount || 0,
        commentCount: track.commentCount || 0,
        creatorId: track.creatorId,
        relevanceScore: score
      }
    })

    // Sort by relevance score (highest first)
    tracks.sort((a, b) => b.relevanceScore - a.relevanceScore)

    // Remove relevance score from final results
    const finalTracks = tracks.map(({ relevanceScore, ...track }) => track)

    // Prepare pagination token
    let nextToken = null
    if (result.LastEvaluatedKey) {
      nextToken = Buffer.from(JSON.stringify(result.LastEvaluatedKey)).toString('base64')
    }

    return successResponse({
      query,
      filters: {
        genre: genre || null,
        aiTool: aiTool || null
      },
      tracks: finalTracks,
      pagination: {
        limit,
        nextToken,
        hasMore: !!result.LastEvaluatedKey
      },
      totalCount: finalTracks.length
    }, 'Search completed successfully')

  } catch (error) {
    console.error('Search tracks error:', error)
    return errorResponse('Failed to search tracks', 500, 'SEARCH_ERROR')
  }
}
