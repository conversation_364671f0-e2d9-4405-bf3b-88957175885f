#!/usr/bin/env node

/**
 * Simple and Reliable Development Server
 * Minimal approach with maximum reliability
 */

const { spawn } = require('child_process');
const path = require('path');
const http = require('http');

// Simple logger
const log = (message, type = 'INFO') => {
  const timestamp = new Date().toLocaleTimeString();
  const colors = {
    INFO: '\x1b[36m',    // cyan
    SUCCESS: '\x1b[32m', // green
    ERROR: '\x1b[31m',   // red
    WARN: '\x1b[33m',    // yellow
    RESET: '\x1b[0m'
  };
  
  const color = colors[type] || colors.INFO;
  console.log(`${color}[${timestamp}] ${message}${colors.RESET}`);
};

// Health check function
const checkHealth = (port) => {
  return new Promise((resolve) => {
    const req = http.get(`http://localhost:${port}/health`, { timeout: 3000 }, (res) => {
      resolve(res.statusCode === 200);
    });
    req.on('error', () => resolve(false));
    req.on('timeout', () => {
      req.destroy();
      resolve(false);
    });
  });
};

// Wait for service to be ready
const waitForService = async (name, port, maxWait = 30000) => {
  const startTime = Date.now();
  log(`Waiting for ${name} to be ready...`, 'INFO');
  
  while (Date.now() - startTime < maxWait) {
    if (await checkHealth(port)) {
      log(`${name} is ready!`, 'SUCCESS');
      return true;
    }
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  throw new Error(`${name} failed to start within ${maxWait}ms`);
};

// Start backend server
const startBackend = () => {
  return new Promise((resolve, reject) => {
    log('Starting backend server...', 'INFO');
    
    const backend = spawn('node', ['scripts/local-express-server.js'], {
      stdio: 'inherit',
      shell: process.platform === 'win32'
    });
    
    backend.on('error', (error) => {
      log(`Backend error: ${error.message}`, 'ERROR');
      reject(error);
    });
    
    // Give backend time to start
    setTimeout(() => {
      if (backend.pid) {
        log('Backend process started', 'SUCCESS');
        resolve(backend);
      } else {
        reject(new Error('Backend failed to start'));
      }
    }, 2000);
  });
};

// Start frontend server
const startFrontend = () => {
  return new Promise((resolve, reject) => {
    log('Starting frontend server...', 'INFO');
    
    const frontend = spawn('npm', ['run', 'dev'], {
      cwd: path.join(process.cwd(), 'frontend'),
      stdio: 'inherit',
      shell: process.platform === 'win32'
    });
    
    frontend.on('error', (error) => {
      log(`Frontend error: ${error.message}`, 'ERROR');
      reject(error);
    });
    
    // Give frontend time to start
    setTimeout(() => {
      if (frontend.pid) {
        log('Frontend process started', 'SUCCESS');
        resolve(frontend);
      } else {
        reject(new Error('Frontend failed to start'));
      }
    }, 3000);
  });
};

// Main function
async function startDevelopment() {
  try {
    log('🚀 Starting Tunami MVP Development Environment', 'INFO');
    log('', 'INFO');
    
    // Setup environment
    log('Setting up environment...', 'INFO');
    const setup = spawn('node', ['scripts/setup-local-env.js'], {
      stdio: 'inherit',
      shell: process.platform === 'win32'
    });
    
    await new Promise((resolve, reject) => {
      setup.on('close', (code) => {
        if (code === 0) {
          log('Environment setup completed', 'SUCCESS');
          resolve();
        } else {
          reject(new Error(`Setup failed with code ${code}`));
        }
      });
    });
    
    log('', 'INFO');
    
    // Start backend
    const backendProcess = await startBackend();
    
    // Wait for backend to be ready
    await waitForService('Backend API', 3001);
    
    log('', 'INFO');
    
    // Start frontend
    const frontendProcess = await startFrontend();
    
    // Wait a bit for frontend to start
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    log('', 'INFO');
    log('🎉 Development environment is ready!', 'SUCCESS');
    log('', 'INFO');
    log('📋 Access Points:', 'INFO');
    log('• Frontend: http://localhost:5173', 'INFO');
    log('• Backend API: http://localhost:3001', 'INFO');
    log('', 'INFO');
    log('💡 Test users available:', 'INFO');
    log('• <EMAIL> / TestPass123!', 'INFO');
    log('• <EMAIL> / TestPass123!', 'INFO');
    log('• <EMAIL> / AdminPass123!', 'INFO');
    log('', 'INFO');
    log('🔄 Servers are running... (Press Ctrl+C to stop)', 'WARN');
    
    // Handle shutdown
    const shutdown = () => {
      log('Shutting down servers...', 'WARN');
      try {
        backendProcess.kill('SIGTERM');
        frontendProcess.kill('SIGTERM');
      } catch (error) {
        // Processes might already be dead
      }
      process.exit(0);
    };
    
    process.on('SIGINT', shutdown);
    process.on('SIGTERM', shutdown);
    
    // Keep process alive
    await new Promise(() => {});
    
  } catch (error) {
    log(`Failed to start development environment: ${error.message}`, 'ERROR');
    process.exit(1);
  }
}

// Start if this file is run directly
if (require.main === module) {
  startDevelopment();
}

module.exports = { startDevelopment };
