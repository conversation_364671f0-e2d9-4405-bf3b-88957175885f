import axios from 'axios'
import { API_BASE_URL, DEFAULT_HEADERS, REQUEST_TIMEOUT } from '../config/api.js'

// Create axios instance
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: REQUEST_TIMEOUT,
  headers: DEFAULT_HEADERS,
})

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('accessToken')
      localStorage.removeItem('idToken')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('user')
      
      // Redirect to login if not already there
      if (window.location.pathname !== '/login') {
        window.location.href = '/login'
      }
    }
    
    return Promise.reject(error)
  }
)

// API Service class
class ApiService {
  /**
   * Register a new user
   * @param {Object} userData - User registration data
   * @returns {Promise<Object>} - Registration response
   */
  async register(userData) {
    try {
      const response = await apiClient.post('/users/register', userData)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  /**
   * Login user
   * @param {Object} credentials - User login credentials
   * @returns {Promise<Object>} - Login response with tokens
   */
  async login(credentials) {
    try {
      const response = await apiClient.post('/users/login', credentials)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  /**
   * Get current user information
   * @returns {Promise<Object>} - Current user data
   */
  async getCurrentUser() {
    try {
      const response = await apiClient.get('/users/me')
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  /**
   * Upload track - get presigned URL for S3 upload
   * @param {Object} uploadData - File upload data
   * @returns {Promise<Object>} - Upload URL and metadata
   */
  async uploadTrack(uploadData) {
    try {
      const response = await apiClient.post('/tracks/upload', uploadData)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  /**
   * Create track metadata
   * @param {Object} trackData - Track metadata
   * @returns {Promise<Object>} - Created track data
   */
  async createTrackMetadata(trackData) {
    try {
      const response = await apiClient.post('/tracks/metadata', trackData)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  /**
   * Get track details by ID
   * @param {string} trackId - Track ID
   * @returns {Promise<Object>} - Track details
   */
  async getTrackDetails(trackId) {
    try {
      const response = await apiClient.get(`/tracks/${trackId}`)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  /**
   * List tracks with optional filtering
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - List of tracks with pagination
   */
  async listTracks(params = {}) {
    try {
      const response = await apiClient.get('/tracks', { params })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  /**
   * Like or unlike a track
   * @param {string} trackId - Track ID
   * @param {string} action - 'like' or 'unlike'
   * @returns {Promise<Object>} - Like response
   */
  async likeTrack(trackId, action = 'like') {
    try {
      const response = await apiClient.post(`/tracks/${trackId}/like`, { action })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  /**
   * Add a comment to a track
   * @param {string} trackId - Track ID
   * @param {string} content - Comment content
   * @returns {Promise<Object>} - Comment response
   */
  async addComment(trackId, content) {
    try {
      const response = await apiClient.post(`/tracks/${trackId}/comments`, { content })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  /**
   * Get comments for a track
   * @param {string} trackId - Track ID
   * @param {number} limit - Number of comments to fetch
   * @returns {Promise<Object>} - Comments response
   */
  async getTrackComments(trackId, limit = 20) {
    try {
      const response = await apiClient.get(`/tracks/${trackId}/comments`, {
        params: { limit }
      })
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  /**
   * Create checkout session for subscription
   * @param {Object} checkoutData - Checkout session data
   * @returns {Promise<Object>} - Checkout session response
   */
  async createCheckoutSession(checkoutData) {
    try {
      const response = await apiClient.post('/payments/checkout', checkoutData)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  /**
   * Create tipping payment intent
   * @param {Object} tipData - Tip payment data
   * @returns {Promise<Object>} - Payment intent response
   */
  async createTippingPaymentIntent(tipData) {
    try {
      const response = await apiClient.post('/payments/tip', tipData)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  /**
   * Handle API errors and format them consistently
   * @param {Error} error - Axios error object
   * @returns {Error} - Formatted error
   */
  handleError(error) {
    if (error.response) {
      // Server responded with error status
      const { data, status } = error.response
      const message = data?.error?.message || data?.message || 'An error occurred'
      const code = data?.error?.code || 'UNKNOWN_ERROR'
      
      const formattedError = new Error(message)
      formattedError.status = status
      formattedError.code = code
      formattedError.details = data?.error?.details || null
      
      return formattedError
    } else if (error.request) {
      // Network error
      const networkError = new Error('Network error. Please check your connection.')
      networkError.status = 0
      networkError.code = 'NETWORK_ERROR'
      
      return networkError
    } else {
      // Other error
      const unknownError = new Error('An unexpected error occurred')
      unknownError.status = 0
      unknownError.code = 'UNKNOWN_ERROR'
      
      return unknownError
    }
  }
}

// Export singleton instance
export default new ApiService()
