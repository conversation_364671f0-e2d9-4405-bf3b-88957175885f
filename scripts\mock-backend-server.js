#!/usr/bin/env node

/**
 * Mock Backend Server for Sprint 5 Admin Panel Testing
 * 
 * This server simulates the AWS Lambda functions locally
 * to test the admin panel functionality without AWS deployment.
 */

const express = require('express')
const cors = require('cors')
const { v4: uuidv4 } = require('uuid')

const app = express()
const PORT = 3001

// Middleware
app.use(cors())
app.use(express.json())

// Mock data storage
let mockReports = [
  {
    id: 'report-1',
    reportId: 'report-1',
    PK: 'REPORT#report-1',
    SK: 'TARGET#TRACK#track-1',
    reporterId: 'user-123',
    contentId: 'track-1',
    contentType: 'track',
    reason: 'inappropriate',
    description: 'This track contains inappropriate content',
    status: 'pending',
    priority: 'medium',
    reportedAt: '2024-06-02T10:30:00Z',
    createdAt: '2024-06-02T10:30:00Z',
    updatedAt: '2024-06-02T10:30:00Z',
    reporter: { username: 'user123', email: '<EMAIL>' },
    target: { 
      id: 'track-1', 
      title: 'AI Symphony No. 1', 
      artist: 'LocalUser',
      type: 'track'
    }
  },
  {
    id: 'report-2',
    reportId: 'report-2',
    PK: 'REPORT#report-2',
    SK: 'TARGET#USER#user-2',
    reporterId: 'user-456',
    reportedUserId: 'user-2',
    contentType: 'user',
    reason: 'harassment',
    description: 'User is harassing other members in comments',
    status: 'pending',
    priority: 'high',
    reportedAt: '2024-06-02T09:15:00Z',
    createdAt: '2024-06-02T09:15:00Z',
    updatedAt: '2024-06-02T09:15:00Z',
    reporter: { username: 'reporter456', email: '<EMAIL>' },
    target: { 
      id: 'user-2', 
      username: 'baduser', 
      email: '<EMAIL>',
      type: 'user'
    }
  }
]

let mockStats = {
  totalReports: 23,
  pendingReports: 8,
  resolvedReports: 15,
  totalUsers: 1247,
  totalTracks: 3891,
  activeUsers: 342
}

// Helper functions
const calculatePriority = (reason) => {
  const highPriorityReasons = ['copyright', 'hate_speech', 'violence', 'harassment']
  const mediumPriorityReasons = ['inappropriate', 'misinformation', 'inappropriate_profile', 'fake_account']
  
  if (highPriorityReasons.includes(reason)) {
    return 'high'
  } else if (mediumPriorityReasons.includes(reason)) {
    return 'medium'
  } else {
    return 'low'
  }
}

// API Routes

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'healthy', message: 'Mock backend server running' })
})

// Report Content
app.post('/dev/reports/content', (req, res) => {
  try {
    const { contentId, contentType, reason, description } = req.body
    
    // Validate required fields
    if (!contentId || !contentType || !reason) {
      return res.status(400).json({
        error: 'Missing required fields: contentId, contentType, reason'
      })
    }

    // Create new report
    const reportId = uuidv4()
    const reportedAt = new Date().toISOString()
    
    const newReport = {
      id: reportId,
      reportId,
      PK: `REPORT#${reportId}`,
      SK: `TARGET#${contentType.toUpperCase()}#${contentId}`,
      reporterId: 'current-user-id', // Would come from auth
      contentId,
      contentType,
      reason,
      description: description || '',
      status: 'pending',
      priority: calculatePriority(reason),
      reportedAt,
      createdAt: reportedAt,
      updatedAt: reportedAt,
      reporter: { username: 'currentuser', email: '<EMAIL>' },
      target: { 
        id: contentId, 
        title: 'Reported Track', 
        artist: 'Some Artist',
        type: contentType
      }
    }

    mockReports.push(newReport)
    mockStats.totalReports++
    mockStats.pendingReports++

    console.log(`📝 New content report created: ${reportId}`)
    
    res.status(201).json({
      message: 'Content reported successfully',
      reportId,
      status: 'pending'
    })
  } catch (error) {
    console.error('Error creating content report:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
})

// Report User
app.post('/dev/reports/user', (req, res) => {
  try {
    const { reportedUserId, reason, description } = req.body
    
    // Validate required fields
    if (!reportedUserId || !reason) {
      return res.status(400).json({
        error: 'Missing required fields: reportedUserId, reason'
      })
    }

    // Create new report
    const reportId = uuidv4()
    const reportedAt = new Date().toISOString()
    
    const newReport = {
      id: reportId,
      reportId,
      PK: `REPORT#${reportId}`,
      SK: `TARGET#USER#${reportedUserId}`,
      reporterId: 'current-user-id', // Would come from auth
      reportedUserId,
      contentType: 'user',
      reason,
      description: description || '',
      status: 'pending',
      priority: calculatePriority(reason),
      reportedAt,
      createdAt: reportedAt,
      updatedAt: reportedAt,
      reporter: { username: 'currentuser', email: '<EMAIL>' },
      target: { 
        id: reportedUserId, 
        username: 'reporteduser', 
        email: '<EMAIL>',
        type: 'user'
      }
    }

    mockReports.push(newReport)
    mockStats.totalReports++
    mockStats.pendingReports++

    console.log(`📝 New user report created: ${reportId}`)
    
    res.status(201).json({
      message: 'User reported successfully',
      reportId,
      status: 'pending'
    })
  } catch (error) {
    console.error('Error creating user report:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
})

// Get Reports Queue (Admin only)
app.get('/dev/admin/reports', (req, res) => {
  try {
    const { status = 'pending', priority, limit = 50 } = req.query
    
    // Filter reports
    let filteredReports = mockReports.filter(report => {
      if (status && report.status !== status) return false
      if (priority && report.priority !== priority) return false
      return true
    })

    // Sort by most recent first
    filteredReports.sort((a, b) => new Date(b.reportedAt) - new Date(a.reportedAt))

    // Limit results
    filteredReports = filteredReports.slice(0, parseInt(limit))

    console.log(`📊 Admin fetched ${filteredReports.length} reports (status: ${status})`)

    res.json({
      reports: filteredReports,
      pagination: {
        hasMore: false,
        lastEvaluatedKey: null,
        count: filteredReports.length
      },
      filters: {
        status,
        priority: priority || 'all'
      }
    })
  } catch (error) {
    console.error('Error fetching reports:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
})

// Take Moderation Action (Admin only)
app.post('/dev/admin/moderation/action', (req, res) => {
  try {
    const { reportId, action } = req.body
    
    if (!reportId || !action) {
      return res.status(400).json({
        error: 'Missing required fields: reportId, action'
      })
    }

    // Find and update report
    const reportIndex = mockReports.findIndex(r => r.id === reportId)
    if (reportIndex === -1) {
      return res.status(404).json({ error: 'Report not found' })
    }

    const report = mockReports[reportIndex]
    const wasResolved = report.status === 'resolved'

    // Update report status
    mockReports[reportIndex] = {
      ...report,
      status: 'resolved',
      action,
      resolvedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    // Update stats
    if (!wasResolved) {
      mockStats.pendingReports--
      mockStats.resolvedReports++
    }

    console.log(`⚖️ Admin took action "${action}" on report ${reportId}`)

    res.json({
      message: `Action ${action} completed successfully`,
      reportId,
      action,
      status: 'resolved'
    })
  } catch (error) {
    console.error('Error taking moderation action:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
})

// Get Platform Stats (Admin only)
app.get('/dev/admin/stats', (req, res) => {
  try {
    console.log('📈 Admin fetched platform statistics')
    res.json(mockStats)
  } catch (error) {
    console.error('Error fetching stats:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
})

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Mock Backend Server running on http://localhost:${PORT}`)
  console.log(`📊 Available endpoints:`)
  console.log(`   POST /dev/reports/content - Report content`)
  console.log(`   POST /dev/reports/user - Report user`)
  console.log(`   GET  /dev/admin/reports - Get reports queue`)
  console.log(`   POST /dev/admin/moderation/action - Take moderation action`)
  console.log(`   GET  /dev/admin/stats - Get platform statistics`)
  console.log(`   GET  /health - Health check`)
  console.log(`\n🎯 Ready for admin panel testing!`)
})
