{"environment": "prod", "aws": {"region": "us-east-1", "stackName": "tunami-mvp-prod"}, "cognito": {"userPoolName": "TunamiUserPool-prod", "userPoolClientName": "TunamiUserPoolClient-prod"}, "dynamodb": {"usersTableName": "TunamiUsers-prod", "tracksTableName": "TunamiTracks-prod", "likesTableName": "TunamiLikes-prod", "commentsTableName": "TunamiComments-prod", "followsTableName": "TunamiFollows-prod", "reportsTableName": "TunamiReports-prod", "subscriptionsTableName": "TunamiSubscriptions-prod", "transactionsTableName": "TunamiTransactions-prod"}, "s3": {"audioFilesBucket": "tunami-audio-files-prod", "staticWebsiteBucket": "tunami-frontend-prod"}, "cloudfront": {"distributionComment": "Tunami MVP Production Distribution"}, "api": {"stageName": "prod", "throttling": {"burstLimit": 1000, "rateLimit": 500}}, "monitoring": {"logRetentionDays": 30, "enableXRay": true}}