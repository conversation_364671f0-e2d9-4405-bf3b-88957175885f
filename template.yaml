AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: Tunami MVP - User Service Infrastructure

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues: [dev, staging, prod]
    Description: Environment name

  StripeSecretKey:
    Type: String
    NoEcho: true
    Description: Stripe Secret Key for payment processing
    Default: sk_test_placeholder

  StripeWebhookSecret:
    Type: String
    NoEcho: true
    Description: Stripe Webhook Secret for signature verification
    Default: whsec_placeholder

Globals:
  Function:
    Timeout: 30
    Runtime: nodejs20.x
    Environment:
      Variables:
        ENVIRONMENT: !Ref Environment
        USER_POOL_ID: !Ref TunamiUserPool
        USER_POOL_CLIENT_ID: !Ref TunamiUserPoolClient
        USERS_TABLE_NAME: !Ref TunamiUsersTable
        TRACKS_TABLE_NAME: !Ref TunamiTracksTable
        LIKES_TABLE_NAME: !Ref TunamiLikesTable
        COMMENTS_TABLE_NAME: !Ref TunamiCommentsTable
        FOLLOWS_TABLE_NAME: !Ref TunamiFollowsTable
        SUBSCRIPTIONS_TABLE_NAME: !Ref TunamiSubscriptionsTable
        TRANSACTIONS_TABLE_NAME: !Ref TunamiTransactionsTable
        REPORTS_TABLE_NAME: !Ref TunamiReportsTable
        AUDIO_BUCKET_NAME: !Ref TunamiAudioBucket
        # CLOUDFRONT_DOMAIN: !GetAtt TunamiAudioCloudFront.DomainName
        STRIPE_SECRET_KEY: !Ref StripeSecretKey
        STRIPE_WEBHOOK_SECRET: !Ref StripeWebhookSecret

Resources:
  # S3 Bucket for Audio Files
  TunamiAudioBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub 'tunami-audio-files-${Environment}'
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders:
              - "*"
            AllowedMethods:
              - GET
              - PUT
              - POST
              - DELETE
              - HEAD
            AllowedOrigins:
              - "*"
            MaxAge: 3000
      LifecycleConfiguration:
        Rules:
          - Id: DeleteIncompleteMultipartUploads
            Status: Enabled
            AbortIncompleteMultipartUpload:
              DaysAfterInitiation: 1
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true

  # CloudFront Distribution for Audio Files (Temporarily Disabled for Sprint 5)
  # TunamiAudioCloudFront:
  #   Type: AWS::CloudFront::Distribution
  #   Properties:
  #     DistributionConfig:
  #       Origins:
  #         - Id: S3Origin
  #           DomainName: !GetAtt TunamiAudioBucket.RegionalDomainName
  #           S3OriginConfig:
  #             OriginAccessIdentity: !Sub 'origin-access-identity/cloudfront/${TunamiCloudFrontOAI}'
  #       Enabled: true
  #       Comment: !Sub 'Tunami Audio CDN - ${Environment}'
  #       DefaultCacheBehavior:
  #         TargetOriginId: S3Origin
  #         ViewerProtocolPolicy: redirect-to-https
  #         AllowedMethods:
  #           - GET
  #           - HEAD
  #           - OPTIONS
  #         CachedMethods:
  #           - GET
  #           - HEAD
  #         ForwardedValues:
  #           QueryString: false
  #           Cookies:
  #             Forward: none
  #           Headers:
  #             - Origin
  #             - Access-Control-Request-Method
  #             - Access-Control-Request-Headers
  #         Compress: true
  #         DefaultTTL: 86400  # 1 day
  #         MaxTTL: 31536000   # 1 year
  #         MinTTL: 0
  #       CacheBehaviors:
  #         - PathPattern: "tracks/*"
  #           TargetOriginId: S3Origin
  #           ViewerProtocolPolicy: redirect-to-https
  #           AllowedMethods:
  #             - GET
  #             - HEAD
  #             - OPTIONS
  #           CachedMethods:
  #             - GET
  #             - HEAD
  #           ForwardedValues:
  #             QueryString: false
  #             Cookies:
  #               Forward: none
  #             Headers:
  #               - Origin
  #               - Access-Control-Request-Method
  #               - Access-Control-Request-Headers
  #           Compress: true
  #           DefaultTTL: 31536000  # 1 year for audio files
  #           MaxTTL: 31536000
  #           MinTTL: 86400
  #       PriceClass: PriceClass_100  # Use only North America and Europe edge locations for cost optimization

  # CloudFront Origin Access Identity (Temporarily Disabled)
  # TunamiCloudFrontOAI:
  #   Type: AWS::CloudFront::OriginAccessIdentity
  #   Properties:
  #     OriginAccessIdentityConfig:
  #       Comment: !Sub 'OAI for Tunami Audio Bucket - ${Environment}'

  # S3 Bucket Policy for CloudFront Access (Temporarily Disabled)
  # TunamiAudioBucketPolicy:
  #   Type: AWS::S3::BucketPolicy
  #   Properties:
  #     Bucket: !Ref TunamiAudioBucket
  #     PolicyDocument:
  #       Statement:
  #         - Sid: AllowCloudFrontAccess
  #           Effect: Allow
  #           Principal:
  #             AWS: !Sub 'arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity ${TunamiCloudFrontOAI}'
  #           Action: 's3:GetObject'
  #           Resource: !Sub '${TunamiAudioBucket}/*'

  # Cognito User Pool
  TunamiUserPool:
    Type: AWS::Cognito::UserPool
    Properties:
      UserPoolName: !Sub tunami-users-${Environment}
      Policies:
        PasswordPolicy:
          MinimumLength: 8
          RequireUppercase: true
          RequireLowercase: true
          RequireNumbers: true
          RequireSymbols: false
      UsernameAttributes:
        - email
      AutoVerifiedAttributes:
        - email
      Schema:
        - Name: email
          AttributeDataType: String
          Required: true
          Mutable: true

  TunamiUserPoolClient:
    Type: AWS::Cognito::UserPoolClient
    Properties:
      UserPoolId: !Ref TunamiUserPool
      ClientName: !Sub tunami-client-${Environment}
      GenerateSecret: false
      ExplicitAuthFlows:
        - ADMIN_NO_SRP_AUTH
        - USER_PASSWORD_AUTH
      TokenValidityUnits:
        AccessToken: hours
        IdToken: hours
        RefreshToken: days
      AccessTokenValidity: 24
      IdTokenValidity: 24
      RefreshTokenValidity: 30

  # DynamoDB Table
  TunamiUsersTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub TunamiUsers-${Environment}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: userId
          AttributeType: S
        - AttributeName: email
          AttributeType: S
      KeySchema:
        - AttributeName: userId
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: EmailIndex
          KeySchema:
            - AttributeName: email
              KeyType: HASH
          Projection:
            ProjectionType: ALL

  # TunamiTracks Table
  TunamiTracksTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub TunamiTracks-${Environment}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: trackId
          AttributeType: S
        - AttributeName: creatorId
          AttributeType: S
        - AttributeName: uploadDate
          AttributeType: S
        - AttributeName: isPublic
          AttributeType: S
      KeySchema:
        - AttributeName: trackId
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: CreatorIdUploadDateIndex
          KeySchema:
            - AttributeName: creatorId
              KeyType: HASH
            - AttributeName: uploadDate
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: IsPublicUploadDateIndex
          KeySchema:
            - AttributeName: isPublic
              KeyType: HASH
            - AttributeName: uploadDate
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

  # TunamiLikes Table
  TunamiLikesTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub TunamiLikes-${Environment}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: userId
          AttributeType: S
        - AttributeName: trackId
          AttributeType: S
      KeySchema:
        - AttributeName: userId
          KeyType: HASH
        - AttributeName: trackId
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: TrackIdUserIdIndex
          KeySchema:
            - AttributeName: trackId
              KeyType: HASH
            - AttributeName: userId
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

  # TunamiComments Table
  TunamiCommentsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub TunamiComments-${Environment}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: commentId
          AttributeType: S
        - AttributeName: trackId
          AttributeType: S
        - AttributeName: createdAt
          AttributeType: S
      KeySchema:
        - AttributeName: commentId
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: TrackIdCreatedAtIndex
          KeySchema:
            - AttributeName: trackId
              KeyType: HASH
            - AttributeName: createdAt
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

  # TunamiFollows Table
  TunamiFollowsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub TunamiFollows-${Environment}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: followerId
          AttributeType: S
        - AttributeName: followingId
          AttributeType: S
      KeySchema:
        - AttributeName: followerId
          KeyType: HASH
        - AttributeName: followingId
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: FollowingIdFollowerIdIndex
          KeySchema:
            - AttributeName: followingId
              KeyType: HASH
            - AttributeName: followerId
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

  # TunamiSubscriptions Table
  TunamiSubscriptionsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub TunamiSubscriptions-${Environment}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: userId
          AttributeType: S
        - AttributeName: subscriptionId
          AttributeType: S
        - AttributeName: status
          AttributeType: S
      KeySchema:
        - AttributeName: userId
          KeyType: HASH
        - AttributeName: subscriptionId
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: StatusIndex
          KeySchema:
            - AttributeName: status
              KeyType: HASH
            - AttributeName: userId
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

  # TunamiTransactions Table
  TunamiTransactionsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub TunamiTransactions-${Environment}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: transactionId
          AttributeType: S
        - AttributeName: userId
          AttributeType: S
        - AttributeName: createdAt
          AttributeType: S
        - AttributeName: type
          AttributeType: S
      KeySchema:
        - AttributeName: transactionId
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: UserIdCreatedAtIndex
          KeySchema:
            - AttributeName: userId
              KeyType: HASH
            - AttributeName: createdAt
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: TypeCreatedAtIndex
          KeySchema:
            - AttributeName: type
              KeyType: HASH
            - AttributeName: createdAt
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

  # TunamiReports Table
  TunamiReportsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub TunamiReports-${Environment}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
        - AttributeName: status
          AttributeType: S
        - AttributeName: reportedAt
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: status-reportedAt-index
          KeySchema:
            - AttributeName: status
              KeyType: HASH
            - AttributeName: reportedAt
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

  # API Gateway
  TunamiApi:
    Type: AWS::Serverless::Api
    Properties:
      StageName: !Ref Environment
      Cors:
        AllowMethods: "'GET,POST,PUT,DELETE,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        AllowOrigin: "'*'"
      Auth:
        Authorizers:
          CognitoAuthorizer:
            UserPoolArn: !GetAtt TunamiUserPool.Arn

  # Lambda Functions
  RegisterUserFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-register-user-${Environment}
      CodeUri: backend/src/
      Handler: handlers/registerUser.handler
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref TunamiUsersTable
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - cognito-idp:AdminCreateUser
                - cognito-idp:AdminSetUserPassword
                - cognito-idp:AdminConfirmSignUp
              Resource: !GetAtt TunamiUserPool.Arn
      Events:
        RegisterUser:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /users/register
            Method: post
        RegisterUserOptions:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /users/register
            Method: options

  LoginUserFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-login-user-${Environment}
      CodeUri: backend/src/
      Handler: handlers/loginUser.handler
      Policies:
        - DynamoDBReadPolicy:
            TableName: !Ref TunamiUsersTable
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - cognito-idp:AdminInitiateAuth
                - cognito-idp:AdminGetUser
              Resource: !GetAtt TunamiUserPool.Arn
            - Effect: Allow
              Action:
                - dynamodb:Query
              Resource: !Sub "${TunamiUsersTable.Arn}/index/*"
      Events:
        LoginUser:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /users/login
            Method: post
        LoginUserOptions:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /users/login
            Method: options

  GetCurrentUserFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-get-current-user-${Environment}
      CodeUri: backend/src/
      Handler: handlers/getCurrentUser.handler
      Policies:
        - DynamoDBReadPolicy:
            TableName: !Ref TunamiUsersTable
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - dynamodb:Query
              Resource: !Sub "${TunamiUsersTable.Arn}/index/*"
      Events:
        GetCurrentUser:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /users/me
            Method: get
            Auth:
              Authorizer: CognitoAuthorizer

  # Track Management Functions
  UploadTrackFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-upload-track-${Environment}
      CodeUri: backend/src/
      Handler: handlers/uploadTrack.handler
      Policies:
        - S3WritePolicy:
            BucketName: !Ref TunamiAudioBucket
      Events:
        UploadTrack:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /tracks/upload
            Method: post
            Auth:
              Authorizer: CognitoAuthorizer

  CreateTrackMetadataFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-create-track-metadata-${Environment}
      CodeUri: backend/src/
      Handler: handlers/createTrackMetadata.handler
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref TunamiTracksTable
      Events:
        CreateTrackMetadata:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /tracks/metadata
            Method: post
            Auth:
              Authorizer: CognitoAuthorizer

  GetTrackDetailsFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-get-track-details-${Environment}
      CodeUri: backend/src/
      Handler: handlers/getTrackDetails.handler
      Policies:
        - DynamoDBReadPolicy:
            TableName: !Ref TunamiTracksTable
        - DynamoDBReadPolicy:
            TableName: !Ref TunamiUsersTable
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - dynamodb:Query
              Resource:
                - !Sub "${TunamiTracksTable.Arn}/index/*"
                - !Sub "${TunamiUsersTable.Arn}/index/*"
      Events:
        GetTrackDetails:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /tracks/{trackId}
            Method: get

  ListAllTracksFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-list-all-tracks-${Environment}
      CodeUri: backend/src/
      Handler: handlers/listAllTracks.handler
      Policies:
        - DynamoDBReadPolicy:
            TableName: !Ref TunamiTracksTable
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - dynamodb:Query
                - dynamodb:Scan
              Resource:
                - !Sub "${TunamiTracksTable.Arn}/index/*"
      Events:
        ListAllTracks:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /tracks
            Method: get

  # Social Features Functions
  LikeTrackFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-like-track-${Environment}
      CodeUri: backend/src/
      Handler: handlers/likeTrack.handler
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref TunamiLikesTable
        - DynamoDBReadPolicy:
            TableName: !Ref TunamiTracksTable
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - dynamodb:UpdateItem
              Resource: !GetAtt TunamiTracksTable.Arn
      Events:
        LikeTrack:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /tracks/{trackId}/like
            Method: post
            Auth:
              Authorizer: CognitoAuthorizer

  AddCommentFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-add-comment-${Environment}
      CodeUri: backend/src/
      Handler: handlers/addComment.handler
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref TunamiCommentsTable
        - DynamoDBReadPolicy:
            TableName: !Ref TunamiTracksTable
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - dynamodb:UpdateItem
              Resource: !GetAtt TunamiTracksTable.Arn
      Events:
        AddComment:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /tracks/{trackId}/comments
            Method: post
            Auth:
              Authorizer: CognitoAuthorizer

  GetTrackCommentsFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-get-track-comments-${Environment}
      CodeUri: backend/src/
      Handler: handlers/getTrackComments.handler
      Policies:
        - DynamoDBReadPolicy:
            TableName: !Ref TunamiCommentsTable
        - DynamoDBReadPolicy:
            TableName: !Ref TunamiUsersTable
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - dynamodb:Query
              Resource:
                - !Sub "${TunamiCommentsTable.Arn}/index/*"
                - !Sub "${TunamiUsersTable.Arn}/index/*"
      Events:
        GetTrackComments:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /tracks/{trackId}/comments
            Method: get

  SearchTracksFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-search-tracks-${Environment}
      CodeUri: backend/src/
      Handler: handlers/searchTracks.handler
      Policies:
        - DynamoDBReadPolicy:
            TableName: !Ref TunamiTracksTable
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - dynamodb:Scan
              Resource: !GetAtt TunamiTracksTable.Arn
      Events:
        SearchTracks:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /search/tracks
            Method: get

  # Monetization Functions
  CreateCheckoutSessionFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-create-checkout-session-${Environment}
      CodeUri: backend/src/
      Handler: handlers/createCheckoutSession.handler
      Timeout: 30
      Events:
        CreateCheckoutSession:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /payments/checkout
            Method: post
            Auth:
              Authorizer: CognitoAuthorizer

  HandleStripeWebhookFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-handle-stripe-webhook-${Environment}
      CodeUri: backend/src/
      Handler: handlers/handleStripeWebhook.handler
      Timeout: 30
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref TunamiSubscriptionsTable
        - DynamoDBCrudPolicy:
            TableName: !Ref TunamiTransactionsTable
      Events:
        HandleStripeWebhook:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /webhooks/stripe
            Method: post

  CreateTippingPaymentIntentFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-create-tipping-payment-intent-${Environment}
      CodeUri: backend/src/
      Handler: handlers/createTippingPaymentIntent.handler
      Timeout: 30
      Policies:
        - DynamoDBReadPolicy:
            TableName: !Ref TunamiUsersTable
        - DynamoDBReadPolicy:
            TableName: !Ref TunamiTracksTable
      Events:
        CreateTippingPaymentIntent:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /payments/tip
            Method: post
            Auth:
              Authorizer: CognitoAuthorizer

  # Admin & Moderation Functions
  ReportContentFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-report-content-${Environment}
      CodeUri: backend/src/
      Handler: handlers/reportContent.handler
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref TunamiReportsTable
      Events:
        ReportContent:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /reports/content
            Method: post
            Auth:
              Authorizer: CognitoAuthorizer

  ReportUserFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-report-user-${Environment}
      CodeUri: backend/src/
      Handler: handlers/reportUser.handler
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref TunamiReportsTable
      Events:
        ReportUser:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /reports/user
            Method: post
            Auth:
              Authorizer: CognitoAuthorizer

  GetReportsQueueFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-get-reports-queue-${Environment}
      CodeUri: backend/src/
      Handler: handlers/getReportsQueue.handler
      Policies:
        - DynamoDBReadPolicy:
            TableName: !Ref TunamiReportsTable
        - DynamoDBReadPolicy:
            TableName: !Ref TunamiUsersTable
        - DynamoDBReadPolicy:
            TableName: !Ref TunamiTracksTable
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - dynamodb:Query
              Resource:
                - !Sub "${TunamiReportsTable.Arn}/index/*"
                - !Sub "${TunamiUsersTable.Arn}/index/*"
                - !Sub "${TunamiTracksTable.Arn}/index/*"
      Events:
        GetReportsQueue:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /admin/reports
            Method: get
            Auth:
              Authorizer: CognitoAuthorizer

Outputs:
  ApiGatewayUrl:
    Description: API Gateway endpoint URL
    Value: !Sub "https://${TunamiApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}"
    Export:
      Name: !Sub ${AWS::StackName}-ApiUrl

  UserPoolId:
    Description: Cognito User Pool ID
    Value: !Ref TunamiUserPool
    Export:
      Name: !Sub ${AWS::StackName}-UserPoolId

  UserPoolClientId:
    Description: Cognito User Pool Client ID
    Value: !Ref TunamiUserPoolClient
    Export:
      Name: !Sub ${AWS::StackName}-UserPoolClientId

  AudioBucketName:
    Description: S3 Bucket for Audio Files
    Value: !Ref TunamiAudioBucket
    Export:
      Name: !Sub ${AWS::StackName}-AudioBucket

  # CloudFrontDomain:
  #   Description: CloudFront Distribution Domain for Audio CDN
  #   Value: !GetAtt TunamiAudioCloudFront.DomainName
  #   Export:
  #     Name: !Sub ${AWS::StackName}-CloudFrontDomain

  # CloudFrontDistributionId:
  #   Description: CloudFront Distribution ID
  #   Value: !Ref TunamiAudioCloudFront
  #   Export:
  #     Name: !Sub ${AWS::StackName}-CloudFrontDistributionId
