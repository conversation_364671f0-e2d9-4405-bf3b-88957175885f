module.exports = {
  env: {
    browser: false,
    es2021: true,
    node: true,
    jest: true
  },
  extends: [
    'standard'
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  rules: {
    'no-console': 'off', // Allow console.log in Lambda functions for CloudWatch logs
    'camelcase': 'off', // AWS SDK uses snake_case in some places
    'no-unused-vars': ['error', { 'argsIgnorePattern': '^_' }]
  },
  overrides: [
    {
      files: ['**/__tests__/**/*', '**/*.test.js', '**/*.spec.js'],
      env: {
        jest: true
      }
    }
  ]
}
