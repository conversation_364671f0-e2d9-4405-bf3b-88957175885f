/**
 * Lambda function to handle Stripe webhook events
 * Processes subscription and payment events securely
 */

const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { 
  DynamoDBDocumentClient, 
  PutCommand, 
  UpdateCommand,
  GetCommand 
} = require('@aws-sdk/lib-dynamodb');

// Initialize DynamoDB client
const client = new DynamoDBClient({
  region: process.env.AWS_REGION || 'us-east-1'
});
const docClient = DynamoDBDocumentClient.from(client);

// Environment variables
const SUBSCRIPTIONS_TABLE = process.env.SUBSCRIPTIONS_TABLE_NAME || 'TunamiSubscriptions';
const TRANSACTIONS_TABLE = process.env.TRANSACTIONS_TABLE_NAME || 'TunamiTransactions';
const STRIPE_WEBHOOK_SECRET = process.env.STRIPE_WEBHOOK_SECRET;

// Mock Stripe for local development
const mockStripe = {
  webhooks: {
    constructEvent: (payload, signature, secret) => {
      // Mock webhook event for local development
      return {
        id: 'evt_mock_' + Date.now(),
        type: 'customer.subscription.created',
        data: {
          object: {
            id: 'sub_mock_' + Date.now(),
            customer: 'cus_mock_customer',
            status: 'active',
            current_period_start: Math.floor(Date.now() / 1000),
            current_period_end: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60),
            metadata: {
              userId: 'local-user'
            }
          }
        }
      };
    }
  }
};

/**
 * Main handler function
 */
exports.handler = async (event) => {
  console.log('Stripe Webhook Event:', JSON.stringify(event, null, 2));

  try {
    const signature = event.headers['stripe-signature'] || event.headers['Stripe-Signature'];
    const payload = event.body;

    // Verify webhook signature (use mock for local development)
    let stripeEvent;
    if (process.env.NODE_ENV === 'development' || !STRIPE_WEBHOOK_SECRET || STRIPE_WEBHOOK_SECRET === 'whsec_placeholder') {
      console.log('Using mock Stripe for local development');
      stripeEvent = mockStripe.webhooks.constructEvent(payload, signature, 'mock_secret');
    } else {
      // In production, use real Stripe
      const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
      stripeEvent = stripe.webhooks.constructEvent(payload, signature, STRIPE_WEBHOOK_SECRET);
    }

    console.log('Processing Stripe event:', stripeEvent.type);

    // Handle different event types
    switch (stripeEvent.type) {
      case 'customer.subscription.created':
        await handleSubscriptionCreated(stripeEvent.data.object);
        break;
      
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(stripeEvent.data.object);
        break;
      
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(stripeEvent.data.object);
        break;
      
      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(stripeEvent.data.object);
        break;
      
      case 'invoice.payment_failed':
        await handlePaymentFailed(stripeEvent.data.object);
        break;
      
      case 'payment_intent.succeeded':
        await handleTipPaymentSucceeded(stripeEvent.data.object);
        break;
      
      default:
        console.log(`Unhandled event type: ${stripeEvent.type}`);
    }

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        received: true,
        eventType: stripeEvent.type,
        eventId: stripeEvent.id
      })
    };

  } catch (error) {
    console.error('Webhook processing error:', error);

    return {
      statusCode: 400,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        error: 'Webhook processing failed',
        message: error.message
      })
    };
  }
};

/**
 * Handle subscription created event
 */
async function handleSubscriptionCreated(subscription) {
  console.log('Processing subscription created:', subscription.id);

  const subscriptionData = {
    userId: subscription.metadata.userId,
    subscriptionId: subscription.id,
    customerId: subscription.customer,
    status: subscription.status,
    planId: 'tunami-supporter', // Our single plan for MVP
    currentPeriodStart: new Date(subscription.current_period_start * 1000).toISOString(),
    currentPeriodEnd: new Date(subscription.current_period_end * 1000).toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  await docClient.send(new PutCommand({
    TableName: SUBSCRIPTIONS_TABLE,
    Item: subscriptionData
  }));

  // Create transaction record
  await createTransactionRecord({
    userId: subscription.metadata.userId,
    type: 'subscription_created',
    amount: 999, // $9.99 in cents
    currency: 'usd',
    stripeSubscriptionId: subscription.id,
    description: 'Tunami Supporter subscription created'
  });

  console.log('Subscription created successfully:', subscription.id);
}

/**
 * Handle subscription updated event
 */
async function handleSubscriptionUpdated(subscription) {
  console.log('Processing subscription updated:', subscription.id);

  await docClient.send(new UpdateCommand({
    TableName: SUBSCRIPTIONS_TABLE,
    Key: {
      userId: subscription.metadata.userId,
      subscriptionId: subscription.id
    },
    UpdateExpression: 'SET #status = :status, currentPeriodStart = :start, currentPeriodEnd = :end, updatedAt = :updatedAt',
    ExpressionAttributeNames: {
      '#status': 'status'
    },
    ExpressionAttributeValues: {
      ':status': subscription.status,
      ':start': new Date(subscription.current_period_start * 1000).toISOString(),
      ':end': new Date(subscription.current_period_end * 1000).toISOString(),
      ':updatedAt': new Date().toISOString()
    }
  }));

  console.log('Subscription updated successfully:', subscription.id);
}

/**
 * Handle subscription deleted event
 */
async function handleSubscriptionDeleted(subscription) {
  console.log('Processing subscription deleted:', subscription.id);

  await docClient.send(new UpdateCommand({
    TableName: SUBSCRIPTIONS_TABLE,
    Key: {
      userId: subscription.metadata.userId,
      subscriptionId: subscription.id
    },
    UpdateExpression: 'SET #status = :status, updatedAt = :updatedAt',
    ExpressionAttributeNames: {
      '#status': 'status'
    },
    ExpressionAttributeValues: {
      ':status': 'canceled',
      ':updatedAt': new Date().toISOString()
    }
  }));

  console.log('Subscription deleted successfully:', subscription.id);
}

/**
 * Handle successful payment event
 */
async function handlePaymentSucceeded(invoice) {
  console.log('Processing payment succeeded:', invoice.id);

  // Create transaction record for successful payment
  await createTransactionRecord({
    userId: invoice.customer_details?.metadata?.userId || 'unknown',
    type: 'subscription_payment',
    amount: invoice.amount_paid,
    currency: invoice.currency,
    stripeInvoiceId: invoice.id,
    description: `Subscription payment for ${invoice.lines.data[0]?.description || 'Tunami Supporter'}`
  });

  console.log('Payment succeeded processed:', invoice.id);
}

/**
 * Handle failed payment event
 */
async function handlePaymentFailed(invoice) {
  console.log('Processing payment failed:', invoice.id);

  // Create transaction record for failed payment
  await createTransactionRecord({
    userId: invoice.customer_details?.metadata?.userId || 'unknown',
    type: 'payment_failed',
    amount: invoice.amount_due,
    currency: invoice.currency,
    stripeInvoiceId: invoice.id,
    description: `Failed payment for ${invoice.lines.data[0]?.description || 'Tunami Supporter'}`,
    status: 'failed'
  });

  console.log('Payment failed processed:', invoice.id);
}

/**
 * Handle tip payment succeeded event
 */
async function handleTipPaymentSucceeded(paymentIntent) {
  console.log('Processing tip payment succeeded:', paymentIntent.id);

  const metadata = paymentIntent.metadata;
  
  // Create transaction record for tip
  await createTransactionRecord({
    userId: metadata.fromUserId,
    type: 'tip_sent',
    amount: paymentIntent.amount,
    currency: paymentIntent.currency,
    stripePaymentIntentId: paymentIntent.id,
    description: `Tip to ${metadata.toUsername || 'creator'}`,
    recipientUserId: metadata.toUserId,
    trackId: metadata.trackId
  });

  // Create transaction record for creator earnings (90% after 10% platform fee)
  const creatorAmount = Math.floor(paymentIntent.amount * 0.9);
  await createTransactionRecord({
    userId: metadata.toUserId,
    type: 'tip_received',
    amount: creatorAmount,
    currency: paymentIntent.currency,
    stripePaymentIntentId: paymentIntent.id,
    description: `Tip received from ${metadata.fromUsername || 'fan'}`,
    senderUserId: metadata.fromUserId,
    trackId: metadata.trackId
  });

  console.log('Tip payment processed:', paymentIntent.id);
}

/**
 * Create transaction record
 */
async function createTransactionRecord(transactionData) {
  const transactionId = `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  const transaction = {
    transactionId,
    userId: transactionData.userId,
    type: transactionData.type,
    amount: transactionData.amount,
    currency: transactionData.currency || 'usd',
    status: transactionData.status || 'completed',
    description: transactionData.description,
    createdAt: new Date().toISOString(),
    ...transactionData
  };

  await docClient.send(new PutCommand({
    TableName: TRANSACTIONS_TABLE,
    Item: transaction
  }));

  console.log('Transaction record created:', transactionId);
}
