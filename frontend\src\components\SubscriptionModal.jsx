import React, { useState } from 'react'
import { X, Crown, Check, Star, Heart, Music, Users } from 'lucide-react'
import { toast } from 'react-hot-toast'
import apiService from '../services/apiService'

const SubscriptionModal = ({ onSuccess, onCancel }) => {
  const [isProcessing, setIsProcessing] = useState(false)

  const features = [
    {
      icon: <Heart className="w-5 h-5" />,
      title: 'Support AI Creators',
      description: 'Help fund the next generation of AI music artists'
    },
    {
      icon: <Star className="w-5 h-5" />,
      title: 'Exclusive Badge',
      description: 'Show your supporter status with a special badge'
    },
    {
      icon: <Music className="w-5 h-5" />,
      title: 'Early Access',
      description: 'Get first listen to new tracks and features'
    },
    {
      icon: <Users className="w-5 h-5" />,
      title: 'Community Access',
      description: 'Join exclusive creator discussions and events'
    }
  ]

  const handleSubscribe = async () => {
    setIsProcessing(true)

    try {
      // Create checkout session
      const response = await apiService.createCheckoutSession({
        planId: 'tunami-supporter',
        successUrl: `${window.location.origin}/subscription/success`,
        cancelUrl: `${window.location.origin}/subscription/cancel`
      })

      if (response.success) {
        // In a real implementation, this would redirect to Stripe Checkout
        console.log('Checkout session created:', response.data)
        
        // For demo purposes, simulate successful subscription
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        toast.success('🎉 Welcome to Tunami Supporter! Thank you for supporting AI creators!')
        onSuccess({
          subscriptionId: response.data.subscriptionId,
          planId: 'tunami-supporter',
          status: 'active'
        })
      } else {
        throw new Error(response.error?.message || 'Failed to create checkout session')
      }
    } catch (error) {
      console.error('Subscription error:', error)
      toast.error('Failed to start subscription. Please try again.')
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-lg w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="relative p-6 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-t-lg">
          <button
            onClick={onCancel}
            className="absolute top-4 right-4 p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
          
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <Crown className="w-6 h-6 text-yellow-300" />
            </div>
            <div>
              <h2 className="text-2xl font-bold">Tunami Supporter</h2>
              <p className="text-purple-100">Join the AI music revolution</p>
            </div>
          </div>

          {/* Pricing */}
          <div className="text-center">
            <div className="text-4xl font-bold mb-1">$9.99</div>
            <div className="text-purple-200">per month</div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Features */}
          <div className="space-y-4 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              What you get as a Supporter:
            </h3>
            
            {features.map((feature, index) => (
              <div key={index} className="flex items-start gap-3">
                <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center text-green-600">
                  {feature.icon}
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">{feature.title}</h4>
                  <p className="text-sm text-gray-600">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>

          {/* Benefits Highlight */}
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2 mb-2">
              <Check className="w-5 h-5 text-green-600" />
              <span className="font-medium text-gray-900">Cancel anytime</span>
            </div>
            <div className="flex items-center gap-2 mb-2">
              <Check className="w-5 h-5 text-green-600" />
              <span className="font-medium text-gray-900">Support goes directly to creators</span>
            </div>
            <div className="flex items-center gap-2">
              <Check className="w-5 h-5 text-green-600" />
              <span className="font-medium text-gray-900">Help shape the future of AI music</span>
            </div>
          </div>

          {/* Impact Statement */}
          <div className="text-center mb-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
            <p className="text-sm text-gray-700">
              <strong>Your support matters!</strong> 80% of your subscription goes directly to 
              supporting AI music creators, helping them create amazing content for the community.
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="flex gap-3 p-6 border-t border-gray-200">
          <button
            onClick={onCancel}
            className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Maybe Later
          </button>
          <button
            onClick={handleSubscribe}
            disabled={isProcessing}
            className="flex-1 px-4 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
          >
            {isProcessing ? 'Processing...' : 'Become a Supporter'}
          </button>
        </div>
      </div>
    </div>
  )
}

export default SubscriptionModal
