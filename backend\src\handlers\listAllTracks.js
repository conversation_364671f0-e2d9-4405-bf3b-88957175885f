const { DynamoDBClient, QueryCommand, ScanCommand } = require('@aws-sdk/client-dynamodb')
const { unmarshall } = require('@aws-sdk/util-dynamodb')
const { successResponse, errorResponse } = require('../utils/responseHelper')
const { validateInput } = require('../utils/validation')
const Joi = require('joi')

const dynamoClient = new DynamoDBClient({
  region: process.env.AWS_REGION,
  maxAttempts: 3,
  retryMode: 'adaptive'
})

// Enhanced validation schema for list tracks query parameters
const listTracksSchema = Joi.object({
  limit: Joi.number().integer().min(1).max(100).default(20),
  lastEvaluatedKey: Joi.string().allow('').default(''),
  creatorId: Joi.string().allow('').default(''),
  publicOnly: Joi.boolean().default(true),
  genre: Joi.string().allow('').max(50).default(''),
  sortBy: Joi.string().valid('recent', 'popular', 'oldest', 'title').default('recent'),
  tags: Joi.string().allow('').default(''), // Comma-separated tags
  search: Joi.string().allow('').max(200).default('') // Search query
})

/**
 * Builds filter expression for additional filtering
 */
const buildFilterExpression = (params, userId) => {
  const filterExpressions = []
  const expressionAttributeValues = {}
  const expressionAttributeNames = {}

  // Genre filter
  if (params.genre) {
    filterExpressions.push('#genre = :genre')
    expressionAttributeNames['#genre'] = 'genre'
    expressionAttributeValues[':genre'] = { S: params.genre }
  }

  // Tags filter (contains any of the specified tags)
  if (params.tags) {
    const tagList = params.tags.split(',').map(tag => tag.trim().toLowerCase()).filter(tag => tag)
    if (tagList.length > 0) {
      const tagConditions = tagList.map((tag, index) => {
        const valueKey = `:tag${index}`
        expressionAttributeValues[valueKey] = { S: tag }
        return `contains(#tags, ${valueKey})`
      })
      filterExpressions.push(`(${tagConditions.join(' OR ')})`)
      expressionAttributeNames['#tags'] = 'tags'
    }
  }

  // Search filter (searches in title, description, and searchableText)
  if (params.search) {
    const searchTerm = params.search.toLowerCase()
    filterExpressions.push('contains(#searchableText, :searchTerm)')
    expressionAttributeNames['#searchableText'] = 'searchableText'
    expressionAttributeValues[':searchTerm'] = { S: searchTerm }
  }

  return {
    filterExpression: filterExpressions.length > 0 ? filterExpressions.join(' AND ') : null,
    expressionAttributeValues,
    expressionAttributeNames
  }
}

/**
 * Enriches track data with creator information (placeholder for future implementation)
 */
const enrichTrackWithCreatorInfo = (track) => {
  // TODO: In Sprint 3, we'll add creator information lookup
  return {
    ...track,
    creator: {
      userId: track.creatorId,
      username: `Creator_${track.creatorId.substring(0, 8)}`, // Placeholder
      profileImageUrl: ''
    }
  }
}

/**
 * Lambda handler for listing tracks with enhanced filtering and pagination
 */
exports.handler = async (event) => {
  console.log('List tracks request:', JSON.stringify(event, null, 2))

  try {
    // Extract user ID from Cognito authorizer (optional)
    const userId = event.requestContext?.authorizer?.claims?.sub

    // Parse and validate query parameters
    const queryParams = event.queryStringParameters || {}
    const validation = validateInput(queryParams, listTracksSchema)

    if (!validation.isValid) {
      console.error('Validation failed:', validation.errors)
      return errorResponse('Invalid query parameters', 400, 'VALIDATION_ERROR', validation.errors)
    }

    const { limit, lastEvaluatedKey, creatorId, publicOnly, genre, sortBy, tags, search } = validation.value

    // Build filter expressions for additional filtering
    const filterConfig = buildFilterExpression({ genre, tags, search }, userId)

    let command
    let indexName = null

    if (creatorId) {
      // Query tracks by specific creator
      indexName = 'CreatorIdUploadDateIndex'
      command = new QueryCommand({
        TableName: process.env.TRACKS_TABLE_NAME,
        IndexName: indexName,
        KeyConditionExpression: 'creatorId = :creatorId',
        ExpressionAttributeValues: {
          ':creatorId': { S: creatorId },
          ...filterConfig.expressionAttributeValues
        },
        ScanIndexForward: sortBy === 'oldest', // Sort by uploadDate
        Limit: limit
      })

      // If not the creator and publicOnly is true, filter for public tracks
      if (creatorId !== userId && publicOnly) {
        const publicFilter = 'isPublic = :isPublic'
        command.ExpressionAttributeValues[':isPublic'] = { S: 'true' }

        if (filterConfig.filterExpression) {
          command.FilterExpression = `${publicFilter} AND ${filterConfig.filterExpression}`
        } else {
          command.FilterExpression = publicFilter
        }
      } else if (filterConfig.filterExpression) {
        command.FilterExpression = filterConfig.filterExpression
      }
    } else {
      // Query all public tracks
      indexName = 'IsPublicUploadDateIndex'
      command = new QueryCommand({
        TableName: process.env.TRACKS_TABLE_NAME,
        IndexName: indexName,
        KeyConditionExpression: 'isPublic = :isPublic',
        ExpressionAttributeValues: {
          ':isPublic': { S: 'true' },
          ...filterConfig.expressionAttributeValues
        },
        ScanIndexForward: sortBy === 'oldest', // Sort by uploadDate
        Limit: limit
      })

      if (filterConfig.filterExpression) {
        command.FilterExpression = filterConfig.filterExpression
      }
    }

    // Add expression attribute names if any
    if (Object.keys(filterConfig.expressionAttributeNames).length > 0) {
      command.ExpressionAttributeNames = filterConfig.expressionAttributeNames
    }

    // Add pagination if provided
    if (lastEvaluatedKey) {
      try {
        command.ExclusiveStartKey = JSON.parse(Buffer.from(lastEvaluatedKey, 'base64').toString())
      } catch (error) {
        console.error('Invalid pagination token:', error)
        return errorResponse('Invalid pagination token', 400, 'INVALID_PAGINATION')
      }
    }

    // Execute the query
    let result
    try {
      result = await dynamoClient.send(command)
    } catch (dbError) {
      console.error('DynamoDB query failed:', dbError)

      if (dbError.name === 'ResourceNotFoundException') {
        return errorResponse('Database table not found', 500, 'TABLE_NOT_FOUND')
      }

      if (dbError.name === 'ValidationException') {
        return errorResponse('Invalid query parameters', 400, 'INVALID_QUERY')
      }

      throw dbError // Re-throw for general error handling
    }

    // Process and enrich tracks
    const tracks = (result.Items || []).map(item => {
      const track = unmarshall(item)

      // Basic track data
      const processedTrack = {
        trackId: track.trackId,
        title: track.title,
        genre: track.genre,
        description: track.description || '',
        aiToolsUsed: track.aiToolsUsed || [],
        audioFileUrl: track.audioFileUrl,
        coverImageUrl: track.coverImageUrl || '',
        uploadDate: track.uploadDate,
        isPublic: track.isPublic === 'true',
        tags: track.tags || [],
        // File metadata
        fileSize: track.fileSize || 0,
        duration: track.duration || null,
        contentType: track.contentType || 'audio/mpeg',
        // Engagement metrics
        listenCount: track.listenCount || 0,
        likeCount: track.likeCount || 0,
        commentCount: track.commentCount || 0,
        // Creator info
        creatorId: track.creatorId,
        isOwner: track.creatorId === userId,
        // Timestamps
        createdAt: track.createdAt,
        updatedAt: track.updatedAt
      }

      // Enrich with creator information
      return enrichTrackWithCreatorInfo(processedTrack)
    })

    // Apply client-side sorting for non-date fields (since DynamoDB GSI only supports date sorting)
    if (sortBy === 'popular') {
      tracks.sort((a, b) => (b.likeCount + b.listenCount) - (a.likeCount + a.listenCount))
    } else if (sortBy === 'title') {
      tracks.sort((a, b) => a.title.localeCompare(b.title))
    }

    // Prepare pagination token
    let nextToken = null
    if (result.LastEvaluatedKey) {
      nextToken = Buffer.from(JSON.stringify(result.LastEvaluatedKey)).toString('base64')
    }

    console.log(`Retrieved ${tracks.length} tracks with filters: genre=${genre}, tags=${tags}, search=${search}, sortBy=${sortBy}`)

    return successResponse({
      tracks,
      pagination: {
        limit,
        nextToken,
        hasMore: !!result.LastEvaluatedKey,
        totalReturned: tracks.length
      },
      filters: {
        creatorId: creatorId || null,
        genre: genre || null,
        tags: tags ? tags.split(',').map(t => t.trim()) : [],
        search: search || null,
        sortBy,
        publicOnly
      }
    }, 'Tracks retrieved successfully')

  } catch (error) {
    console.error('List tracks error:', error)

    // Enhanced error handling
    if (error.name === 'ValidationError') {
      return errorResponse('Invalid query parameters', 400, 'VALIDATION_ERROR', error.details)
    }

    if (error.name === 'AccessDenied') {
      return errorResponse('Access denied to database', 403, 'ACCESS_DENIED')
    }

    if (error.name === 'ThrottlingException') {
      return errorResponse('Service temporarily unavailable', 503, 'SERVICE_THROTTLED')
    }

    return errorResponse('Failed to retrieve tracks', 500, 'RETRIEVAL_ERROR', {
      details: error.message
    })
  }
}
