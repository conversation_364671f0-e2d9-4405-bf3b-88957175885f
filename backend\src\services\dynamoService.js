const { DynamoDBClient } = require('@aws-sdk/client-dynamodb')
const {
  DynamoDBDocumentClient,
  PutCommand,
  GetCommand,
  QueryCommand,
  UpdateCommand
} = require('@aws-sdk/lib-dynamodb')

class DynamoService {
  constructor() {
    // Configure client for local development or AWS
    const clientConfig = {
      region: process.env.AWS_REGION || 'us-east-1'
    }

    // Add local endpoint if running in local environment
    if (process.env.ENVIRONMENT === 'local' && process.env.DYNAMODB_ENDPOINT) {
      clientConfig.endpoint = process.env.DYNAMODB_ENDPOINT
      clientConfig.credentials = {
        accessKeyId: 'dummy',
        secretAccessKey: 'dummy'
      }
    }

    const client = new DynamoDBClient(clientConfig)
    this.docClient = DynamoDBDocumentClient.from(client)
    this.tableName = process.env.USERS_TABLE_NAME
  }

  /**
   * Creates a new user record in DynamoDB
   * @param {Object} userData - User data to store
   * @returns {Promise<Object>} - Created user data
   */
  async createUser(userData) {
    try {
      const timestamp = new Date().toISOString()
      
      const userRecord = {
        userId: userData.userId,
        username: userData.username,
        email: userData.email,
        createdAt: timestamp,
        updatedAt: timestamp,
        isActive: true
      }

      const command = new PutCommand({
        TableName: this.tableName,
        Item: userRecord,
        ConditionExpression: 'attribute_not_exists(userId)'
      })

      await this.docClient.send(command)
      return userRecord
    } catch (error) {
      console.error('Error creating user in DynamoDB:', error)
      
      if (error.name === 'ConditionalCheckFailedException') {
        throw new Error('User already exists')
      }
      
      throw new Error(`Failed to create user: ${error.message}`)
    }
  }

  /**
   * Gets a user by userId
   * @param {string} userId - User ID
   * @returns {Promise<Object|null>} - User data or null if not found
   */
  async getUserById(userId) {
    try {
      const command = new GetCommand({
        TableName: this.tableName,
        Key: {
          userId: userId
        }
      })

      const result = await this.docClient.send(command)
      return result.Item || null
    } catch (error) {
      console.error('Error getting user by ID:', error)
      throw new Error(`Failed to get user: ${error.message}`)
    }
  }

  /**
   * Gets a user by email using GSI
   * @param {string} email - User email
   * @returns {Promise<Object|null>} - User data or null if not found
   */
  async getUserByEmail(email) {
    try {
      const command = new QueryCommand({
        TableName: this.tableName,
        IndexName: 'EmailIndex',
        KeyConditionExpression: 'email = :email',
        ExpressionAttributeValues: {
          ':email': email
        }
      })

      const result = await this.docClient.send(command)
      return result.Items && result.Items.length > 0 ? result.Items[0] : null
    } catch (error) {
      console.error('Error getting user by email:', error)
      throw new Error(`Failed to get user: ${error.message}`)
    }
  }

  /**
   * Updates user information
   * @param {string} userId - User ID
   * @param {Object} updates - Fields to update
   * @returns {Promise<Object>} - Updated user data
   */
  async updateUser(userId, updates) {
    try {
      const timestamp = new Date().toISOString()
      
      // Build update expression dynamically
      const updateExpressions = []
      const expressionAttributeNames = {}
      const expressionAttributeValues = {}
      
      Object.keys(updates).forEach((key, index) => {
        const attrName = `#attr${index}`
        const attrValue = `:val${index}`
        
        updateExpressions.push(`${attrName} = ${attrValue}`)
        expressionAttributeNames[attrName] = key
        expressionAttributeValues[attrValue] = updates[key]
      })
      
      // Always update the updatedAt timestamp
      updateExpressions.push('#updatedAt = :updatedAt')
      expressionAttributeNames['#updatedAt'] = 'updatedAt'
      expressionAttributeValues[':updatedAt'] = timestamp

      const command = new UpdateCommand({
        TableName: this.tableName,
        Key: {
          userId: userId
        },
        UpdateExpression: `SET ${updateExpressions.join(', ')}`,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributeValues,
        ReturnValues: 'ALL_NEW'
      })

      const result = await this.docClient.send(command)
      return result.Attributes
    } catch (error) {
      console.error('Error updating user:', error)
      throw new Error(`Failed to update user: ${error.message}`)
    }
  }

  /**
   * Checks if a user exists by email
   * @param {string} email - User email
   * @returns {Promise<boolean>} - True if user exists, false otherwise
   */
  async userExistsByEmail(email) {
    try {
      const user = await this.getUserByEmail(email)
      return user !== null
    } catch (error) {
      console.error('Error checking if user exists:', error)
      return false
    }
  }
}

module.exports = DynamoService
