name: Backend Deploy
# Pipeline test run - trigger deployment

on:
  push:
    branches:
      - develop
      - main
    paths:
      - 'backend/**'
      - 'template.yaml'
      - '.github/workflows/backend-deploy.yml'
  pull_request:
    branches:
      - develop
      - main
    paths:
      - 'backend/**'
      - 'template.yaml'

env:
  AWS_REGION: us-east-1
  SAM_CLI_TELEMETRY: 0

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: Install dependencies
        working-directory: backend
        run: npm ci

      - name: Run linting
        working-directory: backend
        run: npm run lint

      - name: Run tests
        working-directory: backend
        run: npm run test:coverage

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          directory: backend/coverage

  build-and-deploy-dev:
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    needs: test
    runs-on: ubuntu-latest
    environment: development
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: Setup SAM CLI
        uses: aws-actions/setup-sam@v2
        with:
          use-installer: true

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Install backend dependencies
        working-directory: backend
        run: npm ci --only=production

      - name: Build SAM application
        run: sam build

      - name: Deploy to Dev environment
        run: |
          sam deploy \
            --stack-name tunami-backend-dev \
            --parameter-overrides Environment=dev \
            --capabilities CAPABILITY_IAM \
            --region ${{ env.AWS_REGION }} \
            --no-confirm-changeset \
            --no-fail-on-empty-changeset

  build-and-deploy-staging:
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: test
    runs-on: ubuntu-latest
    environment: staging
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: Setup SAM CLI
        uses: aws-actions/setup-sam@v2
        with:
          use-installer: true

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Install backend dependencies
        working-directory: backend
        run: npm ci --only=production

      - name: Build SAM application
        run: sam build

      - name: Deploy to Staging environment
        run: |
          sam deploy \
            --stack-name tunami-backend-staging \
            --parameter-overrides Environment=staging \
            --capabilities CAPABILITY_IAM \
            --region ${{ env.AWS_REGION }} \
            --no-confirm-changeset \
            --no-fail-on-empty-changeset
