{"environment": "dev", "aws": {"region": "us-east-1", "stackName": "tunami-mvp-dev"}, "cognito": {"userPoolName": "TunamiUserPool-dev", "userPoolClientName": "TunamiUserPoolClient-dev"}, "dynamodb": {"usersTableName": "TunamiUsers-dev", "tracksTableName": "TunamiTracks-dev", "likesTableName": "TunamiLikes-dev", "commentsTableName": "TunamiComments-dev", "followsTableName": "TunamiFollows-dev", "reportsTableName": "TunamiReports-dev", "subscriptionsTableName": "TunamiSubscriptions-dev", "transactionsTableName": "TunamiTransactions-dev"}, "s3": {"audioFilesBucket": "tunami-audio-files-dev", "staticWebsiteBucket": "tunami-frontend-dev"}, "cloudfront": {"distributionComment": "Tunami MVP Development Distribution"}, "api": {"stageName": "dev", "throttling": {"burstLimit": 100, "rateLimit": 50}}, "monitoring": {"logRetentionDays": 7, "enableXRay": false}}