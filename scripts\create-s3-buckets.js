#!/usr/bin/env node

/**
 * Create S3 Buckets Script for Local Development
 * This script creates all required S3 buckets for local development using LocalStack
 */

const { S3Client, CreateBucketCommand, ListBucketsCommand, PutBucketCorsCommand } = require('@aws-sdk/client-s3');

// Configure S3 client for LocalStack
const s3Client = new S3Client({
  region: 'us-east-1',
  endpoint: 'http://localhost:4566',
  credentials: {
    accessKeyId: 'test',
    secretAccessKey: 'test'
  },
  forcePathStyle: true
});

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Bucket definitions
const buckets = [
  {
    name: 'tunami-audio-files-local',
    description: 'Audio files storage for local development'
  },
  {
    name: 'tunami-frontend-local',
    description: 'Frontend static files for local development'
  }
];

// CORS configuration for audio bucket
const corsConfiguration = {
  CORSRules: [
    {
      AllowedHeaders: ['*'],
      AllowedMethods: ['GET', 'PUT', 'POST', 'DELETE', 'HEAD'],
      AllowedOrigins: ['*'],
      MaxAgeSeconds: 3000
    }
  ]
};

async function createBucket(bucketName, description) {
  try {
    const command = new CreateBucketCommand({
      Bucket: bucketName
    });
    
    await s3Client.send(command);
    log(`✅ Created bucket: ${bucketName} (${description})`, 'green');
    
    // Set CORS configuration for audio bucket
    if (bucketName.includes('audio-files')) {
      const corsCommand = new PutBucketCorsCommand({
        Bucket: bucketName,
        CORSConfiguration: corsConfiguration
      });
      
      await s3Client.send(corsCommand);
      log(`✅ Set CORS configuration for: ${bucketName}`, 'green');
    }
    
  } catch (error) {
    if (error.name === 'BucketAlreadyOwnedByYou' || error.name === 'BucketAlreadyExists') {
      log(`ℹ️  Bucket already exists: ${bucketName}`, 'yellow');
    } else {
      log(`❌ Error creating bucket ${bucketName}: ${error.message}`, 'red');
      throw error;
    }
  }
}

async function listExistingBuckets() {
  try {
    const command = new ListBucketsCommand({});
    const response = await s3Client.send(command);
    
    if (response.Buckets && response.Buckets.length > 0) {
      log('📋 Existing buckets:', 'cyan');
      response.Buckets.forEach(bucket => {
        log(`  • ${bucket.Name}`, 'yellow');
      });
      log('', 'reset');
    }
  } catch (error) {
    log(`⚠️  Could not list existing buckets: ${error.message}`, 'yellow');
  }
}

async function main() {
  try {
    log('🪣 Creating S3 buckets for local development...', 'cyan');
    log('', 'reset');

    // List existing buckets first
    await listExistingBuckets();

    // Create buckets
    for (const bucket of buckets) {
      await createBucket(bucket.name, bucket.description);
    }

    log('', 'reset');
    log('✅ All S3 buckets created successfully!', 'green');
    log('', 'reset');
    log('📋 Created buckets:', 'cyan');
    buckets.forEach(bucket => {
      log(`  • ${bucket.name} - ${bucket.description}`, 'yellow');
    });

  } catch (error) {
    log(`❌ Error creating S3 buckets: ${error.message}`, 'red');
    process.exit(1);
  }
}

main();
