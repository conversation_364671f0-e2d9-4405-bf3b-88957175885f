import React, { useState } from 'react'
import { Flag, MoreHorizontal } from 'lucide-react'
import ReportModal from './ReportModal.jsx'

const ReportButton = ({ 
  reportType, // 'content' or 'user'
  targetId, 
  targetInfo, // { title, artist } for content or { username } for user
  variant = 'icon', // 'icon', 'text', or 'dropdown'
  className = ''
}) => {
  const [isReportModalOpen, setIsReportModalOpen] = useState(false)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)

  const handleReportClick = (e) => {
    e.stopPropagation() // Prevent event bubbling
    setIsReportModalOpen(true)
    setIsDropdownOpen(false)
  }

  const handleDropdownToggle = (e) => {
    e.stopPropagation()
    setIsDropdownOpen(!isDropdownOpen)
  }

  // Icon variant - just the flag icon
  if (variant === 'icon') {
    return (
      <>
        <button
          onClick={handleReportClick}
          className={`p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-full transition-colors ${className}`}
          title={`Report ${reportType === 'content' ? 'content' : 'user'}`}
        >
          <Flag className="h-4 w-4" />
        </button>
        
        <ReportModal
          isOpen={isReportModalOpen}
          onClose={() => setIsReportModalOpen(false)}
          reportType={reportType}
          targetId={targetId}
          targetInfo={targetInfo}
        />
      </>
    )
  }

  // Text variant - button with text
  if (variant === 'text') {
    return (
      <>
        <button
          onClick={handleReportClick}
          className={`flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors ${className}`}
        >
          <Flag className="h-4 w-4" />
          <span>Report</span>
        </button>
        
        <ReportModal
          isOpen={isReportModalOpen}
          onClose={() => setIsReportModalOpen(false)}
          reportType={reportType}
          targetId={targetId}
          targetInfo={targetInfo}
        />
      </>
    )
  }

  // Dropdown variant - three dots menu with report option
  if (variant === 'dropdown') {
    return (
      <div className="relative">
        <button
          onClick={handleDropdownToggle}
          className={`p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors ${className}`}
          title="More options"
        >
          <MoreHorizontal className="h-4 w-4" />
        </button>

        {isDropdownOpen && (
          <>
            {/* Backdrop */}
            <div 
              className="fixed inset-0 z-10" 
              onClick={() => setIsDropdownOpen(false)}
            />
            
            {/* Dropdown Menu */}
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-20">
              <div className="py-1">
                <button
                  onClick={handleReportClick}
                  className="flex items-center space-x-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-600"
                >
                  <Flag className="h-4 w-4" />
                  <span>Report {reportType === 'content' ? 'content' : 'user'}</span>
                </button>
                
                {/* Add more dropdown options here if needed */}
                <button
                  onClick={() => setIsDropdownOpen(false)}
                  className="flex items-center space-x-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  <span>Cancel</span>
                </button>
              </div>
            </div>
          </>
        )}

        <ReportModal
          isOpen={isReportModalOpen}
          onClose={() => setIsReportModalOpen(false)}
          reportType={reportType}
          targetId={targetId}
          targetInfo={targetInfo}
        />
      </div>
    )
  }

  return null
}

export default ReportButton
