name: Tunami MVP CI/CD Pipeline

on:
  push:
    branches: [ main, develop, staging ]
  pull_request:
    branches: [ main, develop ]

env:
  AWS_REGION: us-east-1
  NODE_VERSION: '18'

jobs:
  # Job 1: Run Tests
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: |
          backend/package-lock.json
          frontend/package-lock.json

    - name: Install backend dependencies
      run: |
        cd backend
        npm ci

    - name: Install frontend dependencies
      run: |
        cd frontend
        npm ci

    - name: Run backend tests
      run: |
        cd backend
        npm run test:coverage

    - name: Run frontend tests
      run: |
        cd frontend
        npm run test:coverage

    - name: Upload backend coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage/lcov.info
        flags: backend
        name: backend-coverage

    - name: Upload frontend coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend
        name: frontend-coverage

  # Job 2: Build and Deploy to Development
  deploy-dev:
    name: Deploy to Development
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Setup AWS SAM CLI
      uses: aws-actions/setup-sam@v2

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Install backend dependencies
      run: |
        cd backend
        npm ci

    - name: Build SAM application
      run: sam build

    - name: Deploy to Development
      run: |
        sam deploy \
          --stack-name tunami-mvp-dev \
          --parameter-overrides Environment=dev \
          --capabilities CAPABILITY_IAM \
          --region ${{ env.AWS_REGION }} \
          --no-confirm-changeset \
          --no-fail-on-empty-changeset

    - name: Get API Gateway URL
      id: get-api-url
      run: |
        API_URL=$(aws cloudformation describe-stacks \
          --stack-name tunami-mvp-dev \
          --query 'Stacks[0].Outputs[?OutputKey==`TunamiApiUrl`].OutputValue' \
          --output text \
          --region ${{ env.AWS_REGION }})
        echo "api-url=$API_URL" >> $GITHUB_OUTPUT

    - name: Build frontend
      run: |
        cd frontend
        npm ci
        echo "VITE_API_BASE_URL=${{ steps.get-api-url.outputs.api-url }}" > .env.production
        npm run build

    - name: Deploy frontend to S3
      run: |
        aws s3 sync frontend/dist/ s3://${{ secrets.DEV_S3_BUCKET }} --delete

    - name: Invalidate CloudFront cache
      run: |
        aws cloudfront create-invalidation \
          --distribution-id ${{ secrets.DEV_CLOUDFRONT_DISTRIBUTION_ID }} \
          --paths "/*"

    - name: Comment deployment status on PR
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: '🚀 **Development Deployment Successful!**\n\n' +
                  '**Frontend:** https://${{ secrets.DEV_CLOUDFRONT_DOMAIN }}\n' +
                  '**API:** ${{ steps.get-api-url.outputs.api-url }}\n\n' +
                  'Ready for testing! 🎵'
          })

  # Job 3: Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/staging' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Setup AWS SAM CLI
      uses: aws-actions/setup-sam@v2

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Install backend dependencies
      run: |
        cd backend
        npm ci

    - name: Build SAM application
      run: sam build

    - name: Deploy to Staging
      run: |
        sam deploy \
          --stack-name tunami-mvp-staging \
          --parameter-overrides Environment=staging \
          --capabilities CAPABILITY_IAM \
          --region ${{ env.AWS_REGION }} \
          --no-confirm-changeset \
          --no-fail-on-empty-changeset

    - name: Get API Gateway URL
      id: get-api-url
      run: |
        API_URL=$(aws cloudformation describe-stacks \
          --stack-name tunami-mvp-staging \
          --query 'Stacks[0].Outputs[?OutputKey==`TunamiApiUrl`].OutputValue' \
          --output text \
          --region ${{ env.AWS_REGION }})
        echo "api-url=$API_URL" >> $GITHUB_OUTPUT

    - name: Build frontend
      run: |
        cd frontend
        npm ci
        echo "VITE_API_BASE_URL=${{ steps.get-api-url.outputs.api-url }}" > .env.production
        npm run build

    - name: Deploy frontend to S3
      run: |
        aws s3 sync frontend/dist/ s3://${{ secrets.STAGING_S3_BUCKET }} --delete

    - name: Invalidate CloudFront cache
      run: |
        aws cloudfront create-invalidation \
          --distribution-id ${{ secrets.STAGING_CLOUDFRONT_DISTRIBUTION_ID }} \
          --paths "/*"

  # Job 4: Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Setup AWS SAM CLI
      uses: aws-actions/setup-sam@v2

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Install backend dependencies
      run: |
        cd backend
        npm ci

    - name: Build SAM application
      run: sam build

    - name: Deploy to Production
      run: |
        sam deploy \
          --stack-name tunami-mvp-prod \
          --parameter-overrides Environment=prod \
          --capabilities CAPABILITY_IAM \
          --region ${{ env.AWS_REGION }} \
          --no-confirm-changeset \
          --no-fail-on-empty-changeset

    - name: Get API Gateway URL
      id: get-api-url
      run: |
        API_URL=$(aws cloudformation describe-stacks \
          --stack-name tunami-mvp-prod \
          --query 'Stacks[0].Outputs[?OutputKey==`TunamiApiUrl`].OutputValue' \
          --output text \
          --region ${{ env.AWS_REGION }})
        echo "api-url=$API_URL" >> $GITHUB_OUTPUT

    - name: Build frontend
      run: |
        cd frontend
        npm ci
        echo "VITE_API_BASE_URL=${{ steps.get-api-url.outputs.api-url }}" > .env.production
        npm run build

    - name: Deploy frontend to S3
      run: |
        aws s3 sync frontend/dist/ s3://${{ secrets.PROD_S3_BUCKET }} --delete

    - name: Invalidate CloudFront cache
      run: |
        aws cloudfront create-invalidation \
          --distribution-id ${{ secrets.PROD_CLOUDFRONT_DISTRIBUTION_ID }} \
          --paths "/*"

    - name: Create GitHub Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ github.run_number }}
        release_name: Release v${{ github.run_number }}
        body: |
          🚀 **Production Deployment Successful!**
          
          **Changes in this release:**
          ${{ github.event.head_commit.message }}
          
          **Deployed to:**
          - Frontend: https://${{ secrets.PROD_CLOUDFRONT_DOMAIN }}
          - API: ${{ steps.get-api-url.outputs.api-url }}
        draft: false
        prerelease: false
