const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');

const dynamodb = new AWS.DynamoDB.DocumentClient();

const REPORTS_TABLE_NAME = process.env.REPORTS_TABLE_NAME;

// Report categories for users
const USER_REPORT_CATEGORIES = {
  SPAM: 'spam',
  HARASSMENT: 'harassment',
  IMPERSONATION: 'impersonation',
  INAPPROPRIATE_PROFILE: 'inappropriate_profile',
  HATE_SPEECH: 'hate_speech',
  FAKE_ACCOUNT: 'fake_account',
  OTHER: 'other'
};

exports.handler = async (event) => {
  console.log('Report User Event:', JSON.stringify(event, null, 2));

  try {
    // Parse request body
    const body = JSON.parse(event.body);
    const { reportedUserId, reason, description } = body;

    // Get user ID from Cognito authorizer
    const reporterId = event.requestContext.authorizer.claims.sub;

    // Validate required fields
    if (!reportedUserId || !reason) {
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
          'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
          error: 'Missing required fields: reportedUserId, reason'
        })
      };
    }

    // Prevent self-reporting
    if (reporterId === reportedUserId) {
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
          'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
          error: 'You cannot report yourself'
        })
      };
    }

    // Validate reason
    if (!Object.values(USER_REPORT_CATEGORIES).includes(reason)) {
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
          'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
          error: 'Invalid reason. Must be one of: ' + Object.values(USER_REPORT_CATEGORIES).join(', ')
        })
      };
    }

    // Generate report ID and timestamp
    const reportId = uuidv4();
    const reportedAt = new Date().toISOString();

    // Create target key for user
    const targetKey = `TARGET#USER#${reportedUserId}`;

    // Check for duplicate report (same reporter + target)
    try {
      const existingReport = await dynamodb.query({
        TableName: REPORTS_TABLE_NAME,
        IndexName: 'status-reportedAt-index',
        KeyConditionExpression: '#status = :status',
        FilterExpression: 'reporterId = :reporterId AND SK = :targetKey',
        ExpressionAttributeNames: {
          '#status': 'status'
        },
        ExpressionAttributeValues: {
          ':status': 'pending',
          ':reporterId': reporterId,
          ':targetKey': targetKey
        }
      }).promise();

      if (existingReport.Items && existingReport.Items.length > 0) {
        return {
          statusCode: 409,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
          },
          body: JSON.stringify({
            error: 'You have already reported this user'
          })
        };
      }
    } catch (error) {
      console.error('Error checking for duplicate report:', error);
      // Continue with report creation if check fails
    }

    // Create report item
    const reportItem = {
      PK: `REPORT#${reportId}`,
      SK: targetKey,
      reportId,
      reporterId,
      reportedUserId,
      contentType: 'user',
      reason,
      description: description || '',
      status: 'pending',
      reportedAt,
      priority: calculatePriority(reason),
      createdAt: reportedAt,
      updatedAt: reportedAt
    };

    // Save report to DynamoDB
    await dynamodb.put({
      TableName: REPORTS_TABLE_NAME,
      Item: reportItem
    }).promise();

    console.log('User report created successfully:', reportId);

    return {
      statusCode: 201,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
      },
      body: JSON.stringify({
        message: 'User reported successfully',
        reportId,
        status: 'pending'
      })
    };

  } catch (error) {
    console.error('Error reporting user:', error);

    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
      },
      body: JSON.stringify({
        error: 'Internal server error'
      })
    };
  }
};

// Calculate priority based on report reason
function calculatePriority(reason) {
  const highPriorityReasons = ['harassment', 'hate_speech', 'impersonation'];
  const mediumPriorityReasons = ['inappropriate_profile', 'fake_account'];
  
  if (highPriorityReasons.includes(reason)) {
    return 'high';
  } else if (mediumPriorityReasons.includes(reason)) {
    return 'medium';
  } else {
    return 'low';
  }
}
