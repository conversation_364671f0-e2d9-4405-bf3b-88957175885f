AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: Tunami MVP - Local Development Template

Parameters:
  Environment:
    Type: String
    Default: local
    Description: Environment name for local development

Globals:
  Function:
    Timeout: 30
    Runtime: nodejs18.x
    Environment:
      Variables:
        ENVIRONMENT: !Ref Environment
        USER_POOL_ID: local_user_pool
        USER_POOL_CLIENT_ID: local_client_id
        USERS_TABLE_NAME: TunamiUsers-local
        TRACKS_TABLE_NAME: TunamiTracks-local
        LIKES_TABLE_NAME: TunamiLikes-local
        COMMENTS_TABLE_NAME: TunamiComments-local
        FOLLOWS_TABLE_NAME: TunamiFollows-local
        AUDIO_BUCKET_NAME: tunami-audio-files-local
        DYNAMODB_ENDPOINT: http://host.docker.internal:8000
        S3_ENDPOINT: http://host.docker.internal:4566
        AWS_ACCESS_KEY_ID: dummy
        AWS_SECRET_ACCESS_KEY: dummy
    Layers:
      - !Ref DependenciesLayer

Resources:
  # Dependencies Layer for faster local development
  DependenciesLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: tunami-dependencies-local
      Description: Dependencies for Tunami MVP local development
      ContentUri: backend/
      CompatibleRuntimes:
        - nodejs18.x
      RetentionPolicy: Delete

  # API Gateway
  TunamiApi:
    Type: AWS::Serverless::Api
    Properties:
      StageName: !Ref Environment
      Cors:
        AllowMethods: "'GET,POST,PUT,DELETE,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        AllowOrigin: "'*'"

  # Lambda Functions
  RegisterUserFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-register-user-${Environment}
      CodeUri: backend/src/
      Handler: handlers/registerUser.handler
      Events:
        RegisterUser:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /users/register
            Method: post
        RegisterUserOptions:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /users/register
            Method: options

  LoginUserFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-login-user-${Environment}
      CodeUri: backend/src/
      Handler: handlers/loginUser.handler
      Events:
        LoginUser:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /users/login
            Method: post
        LoginUserOptions:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /users/login
            Method: options

  GetCurrentUserFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-get-current-user-${Environment}
      CodeUri: backend/src/
      Handler: handlers/getCurrentUser.handler
      Events:
        GetCurrentUser:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /users/me
            Method: get

  # Track Management Functions
  UploadTrackFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-upload-track-${Environment}
      CodeUri: backend/src/
      Handler: handlers/uploadTrack.handler
      Events:
        UploadTrack:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /tracks/upload
            Method: post

  CreateTrackMetadataFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-create-track-metadata-${Environment}
      CodeUri: backend/src/
      Handler: handlers/createTrackMetadata.handler
      Events:
        CreateTrackMetadata:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /tracks/metadata
            Method: post

  GetTrackDetailsFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-get-track-details-${Environment}
      CodeUri: backend/src/
      Handler: handlers/getTrackDetails.handler
      Events:
        GetTrackDetails:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /tracks/{trackId}
            Method: get

  ListAllTracksFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-list-all-tracks-${Environment}
      CodeUri: backend/src/
      Handler: handlers/listAllTracks.handler
      Events:
        ListAllTracks:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /tracks
            Method: get

  # Social Features Functions
  LikeTrackFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-like-track-${Environment}
      CodeUri: backend/src/
      Handler: handlers/likeTrack.handler
      Events:
        LikeTrack:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /tracks/{trackId}/like
            Method: post

  AddCommentFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-add-comment-${Environment}
      CodeUri: backend/src/
      Handler: handlers/addComment.handler
      Events:
        AddComment:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /tracks/{trackId}/comments
            Method: post

  GetTrackCommentsFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-get-track-comments-${Environment}
      CodeUri: backend/src/
      Handler: handlers/getTrackComments.handler
      Events:
        GetTrackComments:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /tracks/{trackId}/comments
            Method: get

  SearchTracksFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub tunami-search-tracks-${Environment}
      CodeUri: backend/src/
      Handler: handlers/searchTracks.handler
      Events:
        SearchTracks:
          Type: Api
          Properties:
            RestApiId: !Ref TunamiApi
            Path: /search/tracks
            Method: get

Outputs:
  ApiGatewayUrl:
    Description: API Gateway endpoint URL for local development
    Value: !Sub "http://localhost:3001"
    Export:
      Name: !Sub ${AWS::StackName}-ApiUrl
