@echo off
echo 🚀 Starting Tunami MVP Local Development Environment
echo.

echo ✅ Setting up local environment...
call npm run setup:local-env

echo.
echo 🔧 Starting backend server...
start "Tunami Backend" cmd /k "node scripts/local-express-server.js"

echo.
echo ⏳ Waiting for backend to start...
timeout /t 3 /nobreak > nul

echo.
echo 🎨 Starting frontend server...
start "Tunami Frontend" cmd /k "cd frontend && npm run dev"

echo.
echo ✅ Development servers are starting!
echo.
echo 📋 Access Points:
echo • Frontend: http://localhost:5173
echo • Backend API: http://localhost:3001
echo.
echo 💡 Test users available:
echo • <EMAIL> / TestPass123!
echo • <EMAIL> / TestPass123!
echo • <EMAIL> / AdminPass123!
echo.
echo 🔄 Both servers are running in separate windows
echo 📝 Close the command windows to stop the servers
echo.
pause
