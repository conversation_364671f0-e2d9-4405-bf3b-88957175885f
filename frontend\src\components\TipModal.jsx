import React, { useState } from 'react'
import { X, DollarSign, Heart, User, Music } from 'lucide-react'
import { toast } from 'react-hot-toast'
import apiService from '../services/apiService'

const TipModal = ({ 
  trackId, 
  creatorId, 
  creatorUsername, 
  trackTitle, 
  onSuccess, 
  onCancel 
}) => {
  const [selectedAmount, setSelectedAmount] = useState(500) // $5.00 default
  const [customAmount, setCustomAmount] = useState('')
  const [message, setMessage] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [showCustomInput, setShowCustomInput] = useState(false)

  // Predefined tip amounts in cents
  const predefinedAmounts = [
    { value: 100, label: '$1' },
    { value: 300, label: '$3' },
    { value: 500, label: '$5' },
    { value: 1000, label: '$10' },
    { value: 2000, label: '$20' }
  ]

  const handleAmountSelect = (amount) => {
    setSelectedAmount(amount)
    setShowCustomInput(false)
    setCustomAmount('')
  }

  const handleCustomAmountChange = (e) => {
    const value = e.target.value
    setCustomAmount(value)
    
    // Convert to cents and validate
    const amountInCents = Math.round(parseFloat(value) * 100)
    if (!isNaN(amountInCents) && amountInCents >= 100 && amountInCents <= 10000) {
      setSelectedAmount(amountInCents)
    }
  }

  const handleCustomAmountClick = () => {
    setShowCustomInput(true)
    setSelectedAmount(0)
  }

  const calculateFees = (amount) => {
    const platformFee = Math.floor(amount * 0.1) // 10% platform fee
    const creatorAmount = amount - platformFee
    return { platformFee, creatorAmount }
  }

  const handleSendTip = async () => {
    if (selectedAmount < 100) {
      toast.error('Minimum tip amount is $1.00')
      return
    }

    if (selectedAmount > 10000) {
      toast.error('Maximum tip amount is $100.00')
      return
    }

    setIsProcessing(true)

    try {
      // Create payment intent
      const response = await apiService.createTippingPaymentIntent({
        toUserId: creatorId,
        trackId: trackId,
        amount: selectedAmount,
        currency: 'usd',
        message: message.trim()
      })

      if (response.success) {
        // In a real implementation, this would integrate with Stripe Elements
        // For now, we'll simulate a successful payment
        console.log('Payment intent created:', response.data)
        
        // Simulate payment processing delay
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        // Mock successful payment
        const tipData = {
          amount: selectedAmount,
          currency: 'usd',
          recipient: response.data.recipient,
          track: response.data.track,
          platformFee: response.data.platformFee,
          creatorAmount: response.data.creatorAmount
        }
        
        onSuccess(tipData)
      } else {
        throw new Error(response.error?.message || 'Failed to create payment intent')
      }
    } catch (error) {
      console.error('Tip processing error:', error)
      toast.error('Failed to process tip. Please try again.')
    } finally {
      setIsProcessing(false)
    }
  }

  const { platformFee, creatorAmount } = calculateFees(selectedAmount)

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
              <Heart className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Tip Creator</h2>
              <p className="text-sm text-gray-600">Support amazing AI music</p>
            </div>
          </div>
          <button
            onClick={onCancel}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Creator and Track Info */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center gap-3 mb-2">
              <User className="w-5 h-5 text-gray-600" />
              <span className="font-medium text-gray-900">{creatorUsername}</span>
            </div>
            <div className="flex items-center gap-3">
              <Music className="w-5 h-5 text-gray-600" />
              <span className="text-gray-700">{trackTitle}</span>
            </div>
          </div>

          {/* Amount Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Choose tip amount
            </label>
            <div className="grid grid-cols-3 gap-2 mb-3">
              {predefinedAmounts.map((amount) => (
                <button
                  key={amount.value}
                  onClick={() => handleAmountSelect(amount.value)}
                  className={`p-3 rounded-lg border-2 transition-colors ${
                    selectedAmount === amount.value
                      ? 'border-green-500 bg-green-50 text-green-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  {amount.label}
                </button>
              ))}
              <button
                onClick={handleCustomAmountClick}
                className={`p-3 rounded-lg border-2 transition-colors ${
                  showCustomInput
                    ? 'border-green-500 bg-green-50 text-green-700'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                Custom
              </button>
            </div>

            {/* Custom Amount Input */}
            {showCustomInput && (
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-500" />
                <input
                  type="number"
                  value={customAmount}
                  onChange={handleCustomAmountChange}
                  placeholder="0.00"
                  min="1"
                  max="100"
                  step="0.01"
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
            )}
          </div>

          {/* Fee Breakdown */}
          {selectedAmount > 0 && (
            <div className="bg-blue-50 rounded-lg p-4 space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Tip amount:</span>
                <span className="font-medium">${(selectedAmount / 100).toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Platform fee (10%):</span>
                <span className="text-gray-600">-${(platformFee / 100).toFixed(2)}</span>
              </div>
              <div className="border-t border-blue-200 pt-2">
                <div className="flex justify-between text-sm font-medium">
                  <span className="text-gray-900">Creator receives:</span>
                  <span className="text-green-600">${(creatorAmount / 100).toFixed(2)}</span>
                </div>
              </div>
            </div>
          )}

          {/* Optional Message */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Add a message (optional)
            </label>
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Say something nice to the creator..."
              maxLength={500}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
            />
            <div className="text-xs text-gray-500 mt-1">
              {message.length}/500 characters
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex gap-3 p-6 border-t border-gray-200">
          <button
            onClick={onCancel}
            className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSendTip}
            disabled={selectedAmount < 100 || isProcessing}
            className="flex-1 px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-lg hover:from-green-600 hover:to-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isProcessing ? 'Processing...' : `Send $${(selectedAmount / 100).toFixed(2)}`}
          </button>
        </div>
      </div>
    </div>
  )
}

export default TipModal
