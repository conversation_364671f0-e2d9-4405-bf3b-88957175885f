const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001';

// Test functions
async function testHealthCheck() {
  console.log('🔍 Testing health check...');
  try {
    const response = await axios.get(`${API_BASE_URL}/health`);
    console.log('✅ Health check passed:', response.data.message);
    return true;
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    return false;
  }
}

async function testListTracks() {
  console.log('\n🔍 Testing list tracks...');
  try {
    const response = await axios.get(`${API_BASE_URL}/tracks`);
    const { tracks, pagination } = response.data.data;
    console.log(`✅ List tracks passed: ${tracks.length} tracks returned`);
    console.log(`📊 Pagination: hasMore=${pagination.hasMore}, limit=${pagination.limit}`);
    
    // Display first track details
    if (tracks.length > 0) {
      const firstTrack = tracks[0];
      console.log(`🎵 First track: "${firstTrack.title}" by ${firstTrack.creator.username}`);
      console.log(`   Genre: ${firstTrack.genre}, Duration: ${firstTrack.duration}s`);
      console.log(`   AI Tools: ${firstTrack.aiToolsUsed.join(', ')}`);
      console.log(`   Tags: ${firstTrack.tags.map(tag => '#' + tag).join(', ')}`);
    }
    return true;
  } catch (error) {
    console.error('❌ List tracks failed:', error.message);
    return false;
  }
}

async function testSearchTracks() {
  console.log('\n🔍 Testing search functionality...');
  try {
    const searchQuery = 'ambient';
    const response = await axios.get(`${API_BASE_URL}/tracks?search=${searchQuery}`);
    const { tracks, filters } = response.data.data;
    console.log(`✅ Search passed: ${tracks.length} tracks found for "${searchQuery}"`);
    console.log(`🔍 Applied filters:`, filters);
    return true;
  } catch (error) {
    console.error('❌ Search failed:', error.message);
    return false;
  }
}

async function testGenreFilter() {
  console.log('\n🔍 Testing genre filter...');
  try {
    const genre = 'Electronic';
    const response = await axios.get(`${API_BASE_URL}/tracks?genre=${genre}`);
    const { tracks, filters } = response.data.data;
    console.log(`✅ Genre filter passed: ${tracks.length} tracks found for genre "${genre}"`);
    console.log(`🎯 Applied filters:`, filters);
    return true;
  } catch (error) {
    console.error('❌ Genre filter failed:', error.message);
    return false;
  }
}

async function testTagFilter() {
  console.log('\n🔍 Testing tag filter...');
  try {
    const tags = 'electronic,upbeat';
    const response = await axios.get(`${API_BASE_URL}/tracks?tags=${tags}`);
    const { tracks, filters } = response.data.data;
    console.log(`✅ Tag filter passed: ${tracks.length} tracks found for tags "${tags}"`);
    console.log(`🏷️ Applied filters:`, filters);
    return true;
  } catch (error) {
    console.error('❌ Tag filter failed:', error.message);
    return false;
  }
}

async function testSorting() {
  console.log('\n🔍 Testing sorting functionality...');
  try {
    const sortBy = 'popular';
    const response = await axios.get(`${API_BASE_URL}/tracks?sortBy=${sortBy}`);
    const { tracks, filters } = response.data.data;
    console.log(`✅ Sorting passed: ${tracks.length} tracks sorted by "${sortBy}"`);
    console.log(`📈 Applied filters:`, filters);
    
    // Show popularity scores
    if (tracks.length > 0) {
      console.log('📊 Top tracks by popularity:');
      tracks.slice(0, 3).forEach((track, index) => {
        const popularity = track.likeCount + track.listenCount;
        console.log(`   ${index + 1}. "${track.title}" - ${popularity} points (${track.likeCount} likes, ${track.listenCount} plays)`);
      });
    }
    return true;
  } catch (error) {
    console.error('❌ Sorting failed:', error.message);
    return false;
  }
}

async function testUploadFlow() {
  console.log('\n🔍 Testing upload flow...');
  try {
    // Step 1: Get presigned URL
    const uploadData = {
      fileName: 'test-track.mp3',
      fileSize: 5000000,
      contentType: 'audio/mpeg'
    };
    
    const uploadResponse = await axios.post(`${API_BASE_URL}/tracks/upload`, uploadData);
    console.log('✅ Upload URL generation passed');
    console.log(`📤 File key: ${uploadResponse.data.data.fileKey}`);
    console.log(`🔗 S3 URL: ${uploadResponse.data.data.s3Url}`);
    
    // Step 2: Create metadata
    const metadataData = {
      title: 'Test Integration Track',
      genre: 'Experimental',
      description: 'A test track created during integration testing',
      aiToolsUsed: ['TestAI', 'MockGenerator'],
      audioFileUrl: uploadResponse.data.data.s3Url,
      fileKey: uploadResponse.data.data.fileKey,
      isPublic: true,
      tags: ['test', 'integration', 'experimental'],
      fileSize: uploadData.fileSize
    };
    
    const metadataResponse = await axios.post(`${API_BASE_URL}/tracks/metadata`, metadataData);
    console.log('✅ Metadata creation passed');
    console.log(`🎵 Created track: "${metadataResponse.data.data.track.title}"`);
    console.log(`🆔 Track ID: ${metadataResponse.data.data.track.trackId}`);
    
    return true;
  } catch (error) {
    console.error('❌ Upload flow failed:', error.message);
    if (error.response) {
      console.error('   Response:', error.response.data);
    }
    return false;
  }
}

async function testGetTrackDetails() {
  console.log('\n🔍 Testing get track details...');
  try {
    const trackId = 'sample-1'; // Use a track that exists in the backend
    const response = await axios.get(`${API_BASE_URL}/tracks/${trackId}`);
    const track = response.data.data;
    console.log(`✅ Get track details passed for track: "${track.title}"`);
    console.log(`📋 Track details: ${track.genre}, ${track.likeCount} likes`);
    return true;
  } catch (error) {
    console.error('❌ Get track details failed:', error.message);
    return false;
  }
}

// Main test runner
async function runIntegrationTests() {
  console.log('🚀 Starting Tunami MVP Integration Tests\n');
  console.log('=' .repeat(50));
  
  const tests = [
    testHealthCheck,
    testListTracks,
    testSearchTracks,
    testGenreFilter,
    testTagFilter,
    testSorting,
    testUploadFlow,
    testGetTrackDetails
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.error(`❌ Test failed with error:`, error.message);
      failed++;
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log('🎯 Integration Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All integration tests passed! The backend is working perfectly.');
  } else {
    console.log('\n⚠️ Some tests failed. Please check the errors above.');
  }
}

// Run tests
runIntegrationTests().catch(error => {
  console.error('💥 Test runner failed:', error.message);
  process.exit(1);
});
