const AWS = require('aws-sdk');

const dynamodb = new AWS.DynamoDB.DocumentClient();

const REPORTS_TABLE_NAME = process.env.REPORTS_TABLE_NAME;
const USERS_TABLE_NAME = process.env.USERS_TABLE_NAME;
const TRACKS_TABLE_NAME = process.env.TRACKS_TABLE_NAME;

exports.handler = async (event) => {
  console.log('Get Reports Queue Event:', JSON.stringify(event, null, 2));

  try {
    // Check if user is admin (this will be enhanced with proper admin group checking)
    const userGroups = event.requestContext.authorizer.claims['cognito:groups'];
    const isAdmin = userGroups && userGroups.includes('TunamiAdmins');

    if (!isAdmin) {
      return {
        statusCode: 403,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
          'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify({
          error: 'Access denied. Admin privileges required.'
        })
      };
    }

    // Parse query parameters
    const queryParams = event.queryStringParameters || {};
    const status = queryParams.status || 'pending';
    const priority = queryParams.priority;
    const limit = parseInt(queryParams.limit) || 50;
    const lastEvaluatedKey = queryParams.lastEvaluatedKey ? JSON.parse(decodeURIComponent(queryParams.lastEvaluatedKey)) : null;

    // Build query parameters
    const queryParamsObj = {
      TableName: REPORTS_TABLE_NAME,
      IndexName: 'status-reportedAt-index',
      KeyConditionExpression: '#status = :status',
      ExpressionAttributeNames: {
        '#status': 'status'
      },
      ExpressionAttributeValues: {
        ':status': status
      },
      ScanIndexForward: false, // Most recent first
      Limit: limit
    };

    // Add priority filter if specified
    if (priority) {
      queryParamsObj.FilterExpression = 'priority = :priority';
      queryParamsObj.ExpressionAttributeValues[':priority'] = priority;
    }

    // Add pagination
    if (lastEvaluatedKey) {
      queryParamsObj.ExclusiveStartKey = lastEvaluatedKey;
    }

    // Query reports
    const result = await dynamodb.query(queryParamsObj).promise();

    // Enrich reports with additional data
    const enrichedReports = await Promise.all(
      result.Items.map(async (report) => {
        try {
          // Get reporter information
          const reporterInfo = await getUserInfo(report.reporterId);
          
          // Get target information based on content type
          let targetInfo = null;
          if (report.contentType === 'user') {
            targetInfo = await getUserInfo(report.reportedUserId);
          } else if (report.contentType === 'track') {
            targetInfo = await getTrackInfo(report.contentId);
          } else if (report.contentType === 'comment') {
            // For comments, we might need to implement getCommentInfo
            targetInfo = { id: report.contentId, type: 'comment' };
          }

          return {
            ...report,
            reporter: reporterInfo,
            target: targetInfo
          };
        } catch (error) {
          console.error('Error enriching report:', report.reportId, error);
          return report; // Return original report if enrichment fails
        }
      })
    );

    // Prepare response
    const response = {
      reports: enrichedReports,
      pagination: {
        hasMore: !!result.LastEvaluatedKey,
        lastEvaluatedKey: result.LastEvaluatedKey ? encodeURIComponent(JSON.stringify(result.LastEvaluatedKey)) : null,
        count: result.Items.length
      },
      filters: {
        status,
        priority: priority || 'all'
      }
    };

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
      },
      body: JSON.stringify(response)
    };

  } catch (error) {
    console.error('Error getting reports queue:', error);

    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
      },
      body: JSON.stringify({
        error: 'Internal server error'
      })
    };
  }
};

// Helper function to get user information
async function getUserInfo(userId) {
  try {
    const result = await dynamodb.get({
      TableName: USERS_TABLE_NAME,
      Key: { userId }
    }).promise();

    if (result.Item) {
      return {
        userId: result.Item.userId,
        username: result.Item.username,
        email: result.Item.email,
        profilePicture: result.Item.profilePicture
      };
    }
    return { userId, username: 'Unknown User' };
  } catch (error) {
    console.error('Error getting user info:', error);
    return { userId, username: 'Unknown User' };
  }
}

// Helper function to get track information
async function getTrackInfo(trackId) {
  try {
    const result = await dynamodb.get({
      TableName: TRACKS_TABLE_NAME,
      Key: { trackId }
    }).promise();

    if (result.Item) {
      return {
        trackId: result.Item.trackId,
        title: result.Item.title,
        artist: result.Item.artist,
        creatorId: result.Item.creatorId,
        isPublic: result.Item.isPublic
      };
    }
    return { trackId, title: 'Unknown Track' };
  } catch (error) {
    console.error('Error getting track info:', error);
    return { trackId, title: 'Unknown Track' };
  }
}
