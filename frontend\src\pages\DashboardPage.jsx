import React from 'react'
import { useAuth } from '../contexts/AuthContext.jsx'
import { User, Mail, Calendar, CheckCircle } from 'lucide-react'

const DashboardPage = () => {
  const { user } = useAuth()

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Welcome to Tunami MVP
        </h1>
        <p className="text-lg text-gray-600">
          You have successfully logged in to your account!
        </p>
      </div>

      {/* User Profile Card */}
      <div className="max-w-2xl mx-auto">
        <div className="card">
          <div className="flex items-center space-x-4 mb-6">
            <div className="h-16 w-16 bg-primary-100 rounded-full flex items-center justify-center">
              <User className="h-8 w-8 text-primary-600" />
            </div>
            <div>
              <h2 className="text-2xl font-semibold text-gray-900">
                {user?.username}
              </h2>
              <p className="text-gray-600">User Profile</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* User ID */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-sm font-medium text-gray-700">User ID</span>
              </div>
              <p className="text-gray-900 font-mono text-sm bg-gray-50 p-2 rounded">
                {user?.userId}
              </p>
            </div>

            {/* Username */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <User className="h-5 w-5 text-blue-500" />
                <span className="text-sm font-medium text-gray-700">Username</span>
              </div>
              <p className="text-gray-900">{user?.username}</p>
            </div>

            {/* Email */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Mail className="h-5 w-5 text-purple-500" />
                <span className="text-sm font-medium text-gray-700">Email</span>
              </div>
              <p className="text-gray-900">{user?.email}</p>
            </div>

            {/* Created At */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Calendar className="h-5 w-5 text-orange-500" />
                <span className="text-sm font-medium text-gray-700">Member Since</span>
              </div>
              <p className="text-gray-900">
                {user?.createdAt ? formatDate(user.createdAt) : 'N/A'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="max-w-4xl mx-auto">
        <h2 className="text-2xl font-semibold text-gray-900 mb-6 text-center">
          Sprint 1 Features Implemented
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* User Registration */}
          <div className="card text-center">
            <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              User Registration
            </h3>
            <p className="text-gray-600 text-sm">
              Secure user registration with AWS Cognito and DynamoDB storage
            </p>
          </div>

          {/* User Authentication */}
          <div className="card text-center">
            <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              User Authentication
            </h3>
            <p className="text-gray-600 text-sm">
              JWT-based authentication with secure token management
            </p>
          </div>

          {/* User Profile */}
          <div className="card text-center">
            <div className="h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              User Profile
            </h3>
            <p className="text-gray-600 text-sm">
              View and manage user profile information
            </p>
          </div>
        </div>
      </div>

      {/* Technical Stack */}
      <div className="max-w-4xl mx-auto">
        <h2 className="text-2xl font-semibold text-gray-900 mb-6 text-center">
          Technical Stack
        </h2>
        
        <div className="card">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Backend */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Backend</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• AWS Lambda (Node.js)</li>
                <li>• AWS API Gateway</li>
                <li>• AWS Cognito User Pool</li>
                <li>• AWS DynamoDB</li>
                <li>• AWS SAM Framework</li>
                <li>• GitHub Actions CI/CD</li>
              </ul>
            </div>

            {/* Frontend */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Frontend</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• React 18</li>
                <li>• Vite Build Tool</li>
                <li>• Tailwind CSS</li>
                <li>• React Router</li>
                <li>• React Hook Form</li>
                <li>• Axios HTTP Client</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DashboardPage
