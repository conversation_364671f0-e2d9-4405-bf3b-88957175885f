// Minimal HTTP Server for Testing
const http = require('http');

console.log('🔍 Starting minimal HTTP server...');

const server = http.createServer((req, res) => {
  console.log(`📡 ${req.method} ${req.url}`);
  
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  res.setHeader('Content-Type', 'application/json');
  
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  // Route handling
  if (req.url === '/health' && req.method === 'GET') {
    res.writeHead(200);
    res.end(JSON.stringify({ 
      status: 'healthy', 
      message: 'Minimal server running',
      timestamp: new Date().toISOString()
    }));
  } else if (req.url === '/dev/admin/stats' && req.method === 'GET') {
    res.writeHead(200);
    res.end(JSON.stringify({
      totalReports: 10,
      pendingReports: 3,
      resolvedReports: 7,
      totalUsers: 500,
      totalTracks: 200,
      activeUsers: 50
    }));
  } else if (req.url === '/dev/admin/reports' && req.method === 'GET') {
    res.writeHead(200);
    res.end(JSON.stringify({
      reports: [
        {
          id: 'minimal-report-1',
          reason: 'test',
          status: 'pending',
          reportedAt: new Date().toISOString(),
          reporter: { username: 'testuser', email: '<EMAIL>' },
          target: { id: 'track-1', title: 'Test Track', artist: 'Test Artist', type: 'track' },
          description: 'Minimal server test report',
          priority: 'medium'
        }
      ],
      pagination: { hasMore: false, count: 1 }
    }));
  } else if (req.url === '/dev/reports/content' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      console.log('📝 Report data:', body);
      res.writeHead(201);
      res.end(JSON.stringify({
        message: 'Report submitted successfully',
        reportId: 'minimal-' + Date.now(),
        status: 'pending'
      }));
    });
  } else if (req.url === '/dev/admin/moderation/action' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      console.log('⚖️ Action data:', body);
      const data = JSON.parse(body);
      res.writeHead(200);
      res.end(JSON.stringify({
        message: `Action ${data.action} completed successfully`,
        reportId: data.reportId,
        action: data.action,
        status: 'resolved'
      }));
    });
  } else {
    res.writeHead(404);
    res.end(JSON.stringify({ error: 'Not found' }));
  }
});

const PORT = 3001;

server.listen(PORT, (err) => {
  if (err) {
    console.error('❌ Server failed to start:', err);
    process.exit(1);
  }
  console.log(`🚀 Minimal HTTP Server running on http://localhost:${PORT}`);
  console.log('📊 Available endpoints:');
  console.log('   GET  /health');
  console.log('   GET  /dev/admin/stats');
  console.log('   GET  /dev/admin/reports');
  console.log('   POST /dev/reports/content');
  console.log('   POST /dev/admin/moderation/action');
  console.log('\n✅ Server ready for testing!');
});

server.on('error', (err) => {
  console.error('❌ Server error:', err);
  if (err.code === 'EADDRINUSE') {
    console.log(`Port ${PORT} is already in use`);
  }
});

process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});
