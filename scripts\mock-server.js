const express = require('express');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Mock data
let mockTracks = [
  {
    trackId: 'track-1',
    title: 'Ethereal Dreams',
    genre: 'Ambient',
    description: 'A peaceful ambient track created with AI to inspire relaxation and meditation.',
    aiToolsUsed: ['AIVA', 'Soundraw'],
    audioFileUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    coverImageUrl: '',
    uploadDate: '2024-01-15T10:00:00Z',
    isPublic: true,
    tags: ['ambient', 'peaceful', 'meditation'],
    fileSize: 5000000,
    duration: 180,
    contentType: 'audio/mpeg',
    listenCount: 42,
    likeCount: 15,
    commentCount: 5,
    creatorId: 'creator-1',
    creator: {
      userId: 'creator-1',
      username: '<PERSON><PERSON>omposer',
      profileImageUrl: ''
    },
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    trackId: 'track-2',
    title: 'Digital Symphony',
    genre: 'Electronic',
    description: 'An upbeat electronic track showcasing the power of AI music generation.',
    aiToolsUsed: ['Amper Music', 'Boomy'],
    audioFileUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    coverImageUrl: '',
    uploadDate: '2024-01-14T15:30:00Z',
    isPublic: true,
    tags: ['electronic', 'upbeat', 'digital'],
    fileSize: 7200000,
    duration: 240,
    contentType: 'audio/mpeg',
    listenCount: 128,
    likeCount: 34,
    commentCount: 12,
    creatorId: 'creator-2',
    creator: {
      userId: 'creator-2',
      username: 'TechBeats',
      profileImageUrl: ''
    },
    createdAt: '2024-01-14T15:30:00Z',
    updatedAt: '2024-01-14T15:30:00Z'
  },
  {
    trackId: 'track-3',
    title: 'Jazz Fusion AI',
    genre: 'Jazz',
    description: 'A sophisticated jazz fusion piece demonstrating AI creativity in complex musical forms.',
    aiToolsUsed: ['AIVA', 'MuseNet'],
    audioFileUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    coverImageUrl: '',
    uploadDate: '2024-01-13T09:15:00Z',
    isPublic: true,
    tags: ['jazz', 'fusion', 'sophisticated'],
    fileSize: 8500000,
    duration: 300,
    contentType: 'audio/mpeg',
    listenCount: 89,
    likeCount: 28,
    commentCount: 8,
    creatorId: 'creator-3',
    creator: {
      userId: 'creator-3',
      username: 'JazzMaster',
      profileImageUrl: ''
    },
    createdAt: '2024-01-13T09:15:00Z',
    updatedAt: '2024-01-13T09:15:00Z'
  }
];

// Mock user for authentication
const mockUser = {
  userId: 'user-123',
  username: 'TestUser',
  email: '<EMAIL>'
};

// Helper functions
const successResponse = (data, message = 'Success') => ({
  success: true,
  message,
  data
});

const errorResponse = (message, code = 'ERROR', details = null) => ({
  success: false,
  error: {
    message,
    code,
    details
  }
});

const filterTracks = (tracks, filters) => {
  let filtered = [...tracks];

  // Search filter
  if (filters.search) {
    const searchTerm = filters.search.toLowerCase();
    filtered = filtered.filter(track => 
      track.title.toLowerCase().includes(searchTerm) ||
      track.description.toLowerCase().includes(searchTerm) ||
      track.genre.toLowerCase().includes(searchTerm) ||
      track.creator.username.toLowerCase().includes(searchTerm) ||
      track.aiToolsUsed.some(tool => tool.toLowerCase().includes(searchTerm)) ||
      track.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    );
  }

  // Genre filter
  if (filters.genre) {
    filtered = filtered.filter(track => track.genre === filters.genre);
  }

  // Tags filter
  if (filters.tags) {
    const filterTags = filters.tags.split(',').map(tag => tag.trim().toLowerCase());
    filtered = filtered.filter(track => 
      filterTags.some(filterTag => 
        track.tags.some(trackTag => trackTag.toLowerCase().includes(filterTag))
      )
    );
  }

  // Sort
  const sortBy = filters.sortBy || 'recent';
  switch (sortBy) {
    case 'oldest':
      filtered.sort((a, b) => new Date(a.uploadDate) - new Date(b.uploadDate));
      break;
    case 'popular':
      filtered.sort((a, b) => (b.likeCount + b.listenCount) - (a.likeCount + a.listenCount));
      break;
    case 'title':
      filtered.sort((a, b) => a.title.localeCompare(b.title));
      break;
    case 'recent':
    default:
      filtered.sort((a, b) => new Date(b.uploadDate) - new Date(a.uploadDate));
      break;
  }

  return filtered;
};

// Routes

// Health check
app.get('/health', (req, res) => {
  res.json(successResponse({ status: 'healthy' }, 'Mock server is running'));
});

// Authentication endpoints
app.post('/users/register', (req, res) => {
  const { username, email, password } = req.body;

  if (!username || !email || !password) {
    return res.status(400).json(errorResponse('Missing required fields', 'VALIDATION_ERROR'));
  }

  res.json(successResponse({
    message: 'User registered successfully. Please check your email for verification.'
  }, 'Registration successful'));
});

app.post('/users/login', (req, res) => {
  const { email, password } = req.body;

  if (!email || !password) {
    return res.status(400).json(errorResponse('Missing email or password', 'VALIDATION_ERROR'));
  }

  // Mock successful login
  const tokens = {
    accessToken: 'mock-access-token-' + Date.now(),
    idToken: 'mock-id-token-' + Date.now(),
    refreshToken: 'mock-refresh-token-' + Date.now()
  };

  res.json(successResponse({
    user: mockUser,
    tokens
  }, 'Login successful'));
});

app.get('/users/me', (req, res) => {
  // Check for authorization header
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json(errorResponse('No authorization token provided', 'UNAUTHORIZED'));
  }

  // Mock successful user fetch
  res.json(successResponse(mockUser, 'User data retrieved successfully'));
});

// Upload track (get presigned URL)
app.post('/tracks/upload', (req, res) => {
  const { fileName, fileSize, contentType } = req.body;

  if (!fileName || !fileSize || !contentType) {
    return res.status(400).json(errorResponse('Missing required fields', 'VALIDATION_ERROR'));
  }

  const fileKey = `tracks/user-123/${new Date().toISOString().split('T')[0]}/${uuidv4()}.mp3`;
  const mockS3Url = `https://mock-bucket.s3.amazonaws.com/${fileKey}`;

  res.json(successResponse({
    uploadUrl: 'https://mock-presigned-url.s3.amazonaws.com/upload',
    fileKey,
    s3Url: mockS3Url,
    expiresIn: 900,
    maxFileSize: 50 * 1024 * 1024,
    supportedFormats: ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/m4a']
  }, 'Presigned URL generated successfully'));
});

// Create track metadata
app.post('/tracks/metadata', (req, res) => {
  const trackData = req.body;

  if (!trackData.title || !trackData.genre || !trackData.audioFileUrl || !trackData.fileKey) {
    return res.status(400).json(errorResponse('Missing required fields', 'VALIDATION_ERROR'));
  }

  const newTrack = {
    trackId: uuidv4(),
    ...trackData,
    creatorId: 'user-123',
    uploadDate: new Date().toISOString(),
    listenCount: 0,
    likeCount: 0,
    commentCount: 0,
    creator: {
      userId: 'user-123',
      username: 'TestUser',
      profileImageUrl: ''
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  mockTracks.unshift(newTrack);

  res.json(successResponse({
    track: newTrack
  }, 'Track metadata created successfully'));
});

// List tracks with filtering
app.get('/tracks', (req, res) => {
  const filters = req.query;
  const limit = parseInt(filters.limit) || 20;

  const filteredTracks = filterTracks(mockTracks, filters);
  const paginatedTracks = filteredTracks.slice(0, limit);

  res.json(successResponse({
    tracks: paginatedTracks,
    pagination: {
      limit,
      nextToken: null,
      hasMore: filteredTracks.length > limit,
      totalReturned: paginatedTracks.length
    },
    filters: {
      search: filters.search || null,
      genre: filters.genre || null,
      tags: filters.tags ? filters.tags.split(',').map(t => t.trim()) : [],
      sortBy: filters.sortBy || 'recent'
    }
  }, 'Tracks retrieved successfully'));
});

// Get track details
app.get('/tracks/:trackId', (req, res) => {
  const { trackId } = req.params;
  const track = mockTracks.find(t => t.trackId === trackId);

  if (!track) {
    return res.status(404).json(errorResponse('Track not found', 'TRACK_NOT_FOUND'));
  }

  res.json(successResponse({ track }, 'Track details retrieved successfully'));
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json(errorResponse('Internal server error', 'SERVER_ERROR'));
});

// 404 handler
app.use((req, res) => {
  res.status(404).json(errorResponse('Endpoint not found', 'NOT_FOUND'));
});

// Start server
app.listen(PORT, () => {
  console.log(`🎵 Tunami Mock Server running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🎧 Mock tracks available: ${mockTracks.length}`);
});
