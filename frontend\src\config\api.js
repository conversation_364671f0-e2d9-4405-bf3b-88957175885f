// API Configuration
const config = {
  local: {
    apiBaseUrl: 'http://localhost:3001',
  },
  development: {
    apiBaseUrl: 'https://lg0jagab28.execute-api.us-east-1.amazonaws.com/dev',
  },
  staging: {
    apiBaseUrl: 'https://lg0jagab28.execute-api.us-east-1.amazonaws.com/staging',
  },
  production: {
    apiBaseUrl: 'https://lg0jagab28.execute-api.us-east-1.amazonaws.com/prod',
  }
}

// Determine environment
const getEnvironment = () => {
  // Check for environment variable first (for local development)
  if (import.meta.env.VITE_ENVIRONMENT) {
    return import.meta.env.VITE_ENVIRONMENT;
  }

  const hostname = window.location.hostname

  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return 'local'
  } else if (hostname.includes('staging')) {
    return 'staging'
  } else {
    return 'production'
  }
}

const environment = getEnvironment()
const apiConfig = config[environment]

export const API_BASE_URL = apiConfig.apiBaseUrl

// API Endpoints
export const API_ENDPOINTS = {
  // User endpoints
  REGISTER: '/users/register',
  LOGIN: '/users/login',
  CURRENT_USER: '/users/me',

  // Track endpoints
  UPLOAD_TRACK: '/tracks/upload',
  CREATE_TRACK_METADATA: '/tracks/metadata',
  GET_TRACK_DETAILS: '/tracks',
  LIST_ALL_TRACKS: '/tracks',
  SEARCH_TRACKS: '/search/tracks',

  // Social endpoints
  LIKE_TRACK: '/tracks/{trackId}/like',
  ADD_COMMENT: '/tracks/{trackId}/comments',
  GET_TRACK_COMMENTS: '/tracks/{trackId}/comments',
}

// Default headers
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
}

// Request timeout (in milliseconds)
export const REQUEST_TIMEOUT = 10000
