// Working Server with Self-Test
console.log('🚀 Starting working server...');

const http = require('http');

// Mock data
const mockData = {
  stats: {
    totalReports: 15,
    pendingReports: 5,
    resolvedReports: 10,
    totalUsers: 750,
    totalTracks: 300,
    activeUsers: 85
  },
  reports: [
    {
      id: 'report-001',
      reason: 'inappropriate',
      status: 'pending',
      reportedAt: new Date().toISOString(),
      reporter: { username: 'user123', email: '<EMAIL>' },
      target: { 
        id: 'track-001', 
        title: 'AI Generated Beat', 
        artist: 'TestUser',
        type: 'track'
      },
      description: 'This track contains inappropriate content',
      priority: 'medium'
    },
    {
      id: 'report-002',
      reason: 'harassment',
      status: 'pending',
      reportedAt: new Date().toISOString(),
      reporter: { username: 'reporter456', email: '<EMAIL>' },
      target: { 
        id: 'user-002', 
        username: 'baduser', 
        email: '<EMAIL>',
        type: 'user'
      },
      description: 'User is harassing others in comments',
      priority: 'high'
    }
  ]
};

const server = http.createServer((req, res) => {
  console.log(`📡 ${new Date().toISOString()} - ${req.method} ${req.url}`);
  
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  res.setHeader('Content-Type', 'application/json');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  try {
    if (req.url === '/health' && req.method === 'GET') {
      const response = { 
        status: 'healthy', 
        message: 'Working server is running perfectly!',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      };
      res.writeHead(200);
      res.end(JSON.stringify(response, null, 2));
      
    } else if (req.url === '/dev/admin/stats' && req.method === 'GET') {
      res.writeHead(200);
      res.end(JSON.stringify(mockData.stats, null, 2));
      
    } else if (req.url.startsWith('/dev/admin/reports') && req.method === 'GET') {
      const response = {
        reports: mockData.reports,
        pagination: { hasMore: false, count: mockData.reports.length }
      };
      res.writeHead(200);
      res.end(JSON.stringify(response, null, 2));
      
    } else if (req.url === '/dev/reports/content' && req.method === 'POST') {
      let body = '';
      req.on('data', chunk => body += chunk.toString());
      req.on('end', () => {
        try {
          const reportData = JSON.parse(body);
          console.log('📝 New report received:', reportData);
          
          const newReport = {
            id: 'report-' + Date.now(),
            ...reportData,
            status: 'pending',
            reportedAt: new Date().toISOString(),
            priority: 'medium'
          };
          
          mockData.reports.push(newReport);
          mockData.stats.totalReports++;
          mockData.stats.pendingReports++;
          
          const response = {
            message: 'Content reported successfully',
            reportId: newReport.id,
            status: 'pending'
          };
          
          res.writeHead(201);
          res.end(JSON.stringify(response, null, 2));
        } catch (error) {
          console.error('❌ Error processing report:', error);
          res.writeHead(400);
          res.end(JSON.stringify({ error: 'Invalid JSON' }));
        }
      });
      
    } else if (req.url === '/dev/admin/moderation/action' && req.method === 'POST') {
      let body = '';
      req.on('data', chunk => body += chunk.toString());
      req.on('end', () => {
        try {
          const actionData = JSON.parse(body);
          console.log('⚖️ Moderation action received:', actionData);
          
          const { reportId, action } = actionData;
          const report = mockData.reports.find(r => r.id === reportId);
          
          if (report && report.status === 'pending') {
            report.status = 'resolved';
            report.action = action;
            report.resolvedAt = new Date().toISOString();
            
            mockData.stats.pendingReports--;
            mockData.stats.resolvedReports++;
          }
          
          const response = {
            message: `Action ${action} completed successfully`,
            reportId,
            action,
            status: 'resolved'
          };
          
          res.writeHead(200);
          res.end(JSON.stringify(response, null, 2));
        } catch (error) {
          console.error('❌ Error processing action:', error);
          res.writeHead(400);
          res.end(JSON.stringify({ error: 'Invalid JSON' }));
        }
      });
      
    } else {
      res.writeHead(404);
      res.end(JSON.stringify({ error: 'Endpoint not found' }));
    }
  } catch (error) {
    console.error('❌ Server error:', error);
    res.writeHead(500);
    res.end(JSON.stringify({ error: 'Internal server error' }));
  }
});

const PORT = 3001;

server.listen(PORT, '127.0.0.1', () => {
  console.log(`🚀 Working Server started successfully!`);
  console.log(`📡 Server running on http://localhost:${PORT}`);
  console.log(`🕐 Started at: ${new Date().toISOString()}`);
  console.log('📊 Available endpoints:');
  console.log('   GET  /health - Health check');
  console.log('   GET  /dev/admin/stats - Platform statistics');
  console.log('   GET  /dev/admin/reports - Reports queue');
  console.log('   POST /dev/reports/content - Submit content report');
  console.log('   POST /dev/admin/moderation/action - Take moderation action');
  console.log('\n✅ Server is ready for admin panel testing!');
  
  // Self-test
  setTimeout(() => {
    console.log('\n🧪 Running self-test...');
    const testReq = http.request({
      hostname: 'localhost',
      port: PORT,
      path: '/health',
      method: 'GET'
    }, (testRes) => {
      let data = '';
      testRes.on('data', chunk => data += chunk);
      testRes.on('end', () => {
        console.log('✅ Self-test passed! Server is responding correctly.');
        console.log('📋 Health check response:', JSON.parse(data).message);
      });
    });
    
    testReq.on('error', (err) => {
      console.error('❌ Self-test failed:', err.message);
    });
    
    testReq.end();
  }, 1000);
});

server.on('error', (err) => {
  console.error('❌ Server error:', err);
  if (err.code === 'EADDRINUSE') {
    console.log(`❌ Port ${PORT} is already in use. Trying to find and kill existing process...`);
    process.exit(1);
  }
});

process.on('SIGINT', () => {
  console.log('\n🛑 Received shutdown signal...');
  server.close(() => {
    console.log('✅ Server closed gracefully');
    process.exit(0);
  });
});

console.log('🔧 Server setup complete, starting listener...');
