const { DynamoDBClient, Put<PERSON>temCommand, DeleteItemCommand, GetItemCommand, UpdateItemCommand } = require('@aws-sdk/client-dynamodb')
const { marshall, unmarshall } = require('@aws-sdk/util-dynamodb')
const { successResponse, errorResponse } = require('../utils/responseHelper')

const dynamoClient = new DynamoDBClient({ region: process.env.AWS_REGION })

/**
 * Lambda handler for liking/unliking tracks
 */
exports.handler = async (event) => {
  console.log('Like track request:', JSON.stringify(event, null, 2))

  try {
    // Extract user ID from Cognito authorizer
    const userId = event.requestContext?.authorizer?.claims?.sub
    if (!userId) {
      return errorResponse('User not authenticated', 401, 'UNAUTHORIZED')
    }

    // Extract track ID from path parameters
    const trackId = event.pathParameters?.trackId
    if (!trackId) {
      return errorResponse('Track ID is required', 400, 'MISSING_TRACK_ID')
    }

    // Parse request body to determine action (like/unlike)
    const body = JSON.parse(event.body || '{}')
    const action = body.action || 'like' // Default to like

    if (!['like', 'unlike'].includes(action)) {
      return errorResponse('Invalid action. Must be "like" or "unlike"', 400, 'INVALID_ACTION')
    }

    // Check if track exists
    const trackExists = await checkTrackExists(trackId)
    if (!trackExists) {
      return errorResponse('Track not found', 404, 'TRACK_NOT_FOUND')
    }

    // Check current like status
    const currentLike = await getCurrentLike(userId, trackId)
    const isCurrentlyLiked = !!currentLike

    if (action === 'like' && isCurrentlyLiked) {
      return errorResponse('Track already liked', 409, 'ALREADY_LIKED')
    }

    if (action === 'unlike' && !isCurrentlyLiked) {
      return errorResponse('Track not liked', 409, 'NOT_LIKED')
    }

    // Perform the like/unlike operation
    if (action === 'like') {
      await addLike(userId, trackId)
      await updateTrackLikeCount(trackId, 1)
    } else {
      await removeLike(userId, trackId)
      await updateTrackLikeCount(trackId, -1)
    }

    return successResponse({
      trackId,
      userId,
      action,
      liked: action === 'like'
    }, `Track ${action}d successfully`)

  } catch (error) {
    console.error('Like track error:', error)
    return errorResponse('Failed to process like action', 500, 'LIKE_ERROR')
  }
}

/**
 * Check if track exists
 */
async function checkTrackExists(trackId) {
  try {
    const getCommand = new GetItemCommand({
      TableName: process.env.TRACKS_TABLE_NAME,
      Key: {
        trackId: { S: trackId }
      }
    })

    const result = await dynamoClient.send(getCommand)
    return !!result.Item
  } catch (error) {
    console.error('Error checking track existence:', error)
    return false
  }
}

/**
 * Get current like status
 */
async function getCurrentLike(userId, trackId) {
  try {
    const getCommand = new GetItemCommand({
      TableName: process.env.LIKES_TABLE_NAME,
      Key: {
        userId: { S: userId },
        trackId: { S: trackId }
      }
    })

    const result = await dynamoClient.send(getCommand)
    return result.Item ? unmarshall(result.Item) : null
  } catch (error) {
    console.error('Error getting current like:', error)
    return null
  }
}

/**
 * Add a like
 */
async function addLike(userId, trackId) {
  const likeData = {
    userId,
    trackId,
    createdAt: new Date().toISOString()
  }

  const putCommand = new PutItemCommand({
    TableName: process.env.LIKES_TABLE_NAME,
    Item: marshall(likeData),
    ConditionExpression: 'attribute_not_exists(userId) AND attribute_not_exists(trackId)'
  })

  await dynamoClient.send(putCommand)
}

/**
 * Remove a like
 */
async function removeLike(userId, trackId) {
  const deleteCommand = new DeleteItemCommand({
    TableName: process.env.LIKES_TABLE_NAME,
    Key: {
      userId: { S: userId },
      trackId: { S: trackId }
    }
  })

  await dynamoClient.send(deleteCommand)
}

/**
 * Update track like count
 */
async function updateTrackLikeCount(trackId, increment) {
  const updateCommand = new UpdateItemCommand({
    TableName: process.env.TRACKS_TABLE_NAME,
    Key: {
      trackId: { S: trackId }
    },
    UpdateExpression: 'ADD likeCount :increment',
    ExpressionAttributeValues: {
      ':increment': { N: increment.toString() }
    }
  })

  await dynamoClient.send(updateCommand)
}
