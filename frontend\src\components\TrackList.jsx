import React, { useState, useEffect } from 'react'
import { Play, Pause, Heart, MessageCircle, Clock, User } from 'lucide-react'
import apiService from '../services/apiService'
import { useAuth } from '../contexts/AuthContext'
import LoadingSpinner from './LoadingSpinner'
import LikeButton from './LikeButton'
import CommentSection from './CommentSection'
import TipButton from './TipButton'
import ReportButton from './ReportButton'
import toast from 'react-hot-toast'

const TrackCard = ({ track, isPlaying, onPlay, onPause, currentTrack }) => {
  const { user } = useAuth()
  const isCurrentTrack = currentTrack?.trackId === track.trackId

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatDuration = (seconds) => {
    if (!seconds) return '0:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const handlePlayPause = () => {
    if (isCurrentTrack && isPlaying) {
      onPause()
    } else {
      onPlay(track)
    }
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow">
      <div className="p-4">
        {/* Track Header */}
        <div className="flex items-start gap-4 mb-3">
          {/* Cover Art */}
          <div className="relative">
            {track.coverImageUrl ? (
              <img
                src={track.coverImageUrl}
                alt={track.title}
                className="w-16 h-16 rounded-lg object-cover"
              />
            ) : (
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">
                  {track.title.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
            
            {/* Play Button Overlay */}
            <button
              onClick={handlePlayPause}
              className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity"
            >
              {isCurrentTrack && isPlaying ? (
                <Pause className="w-6 h-6 text-white" />
              ) : (
                <Play className="w-6 h-6 text-white ml-0.5" />
              )}
            </button>
          </div>

          {/* Track Info */}
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-gray-900 text-lg mb-1 truncate">
              {track.title}
            </h3>
            
            <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
              <User className="w-4 h-4" />
              <span>{track.creator?.username || 'Unknown Artist'}</span>
              <span>•</span>
              <span className="bg-gray-100 px-2 py-1 rounded-full text-xs">
                {track.genre}
              </span>
            </div>

            <div className="flex items-center gap-4 text-sm text-gray-500">
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                <span>{formatDate(track.uploadDate)}</span>
              </div>

              <div className="flex items-center gap-1">
                <Play className="w-4 h-4" />
                <span>{track.listenCount || 0} plays</span>
              </div>
            </div>
          </div>

          {/* Play Button */}
          <button
            onClick={handlePlayPause}
            className={`p-3 rounded-full transition-colors ${
              isCurrentTrack && isPlaying
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            {isCurrentTrack && isPlaying ? (
              <Pause className="w-5 h-5" />
            ) : (
              <Play className="w-5 h-5 ml-0.5" />
            )}
          </button>
        </div>

        {/* Description */}
        {track.description && (
          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
            {track.description}
          </p>
        )}

        {/* AI Tools Used */}
        {track.aiToolsUsed && track.aiToolsUsed.length > 0 && (
          <div className="mb-3">
            <p className="text-xs text-gray-500 mb-1">AI Tools Used:</p>
            <div className="flex flex-wrap gap-1">
              {track.aiToolsUsed.map((tool, index) => (
                <span
                  key={index}
                  className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
                >
                  {tool}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Tags */}
        {track.tags && track.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-4">
            {track.tags.map((tag, index) => (
              <span
                key={index}
                className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full"
              >
                #{tag}
              </span>
            ))}
          </div>
        )}

        {/* Social Features */}
        <div className="border-t border-gray-100 pt-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <LikeButton
                trackId={track.trackId}
                initialLikeCount={track.likeCount || 0}
                initialIsLiked={false} // TODO: Get user's like status from API
                size="sm"
              />
              <TipButton
                trackId={track.trackId}
                creatorId={track.creatorId}
                creatorUsername={track.creator?.username || 'Unknown Artist'}
                trackTitle={track.title}
                size="sm"
                variant="minimal"
              />
            </div>

            {/* Report Button */}
            <div className="flex items-center">
              <ReportButton
                reportType="content"
                targetId={track.trackId}
                targetInfo={{
                  title: track.title,
                  artist: track.creator?.username || 'Unknown Artist'
                }}
                variant="icon"
              />
            </div>
          </div>

          <div className="mt-3">
            <CommentSection
              trackId={track.trackId}
              initialCommentCount={track.commentCount || 0}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

const TrackList = ({
  creatorId = null,
  filters = {},
  onTrackSelect,
  currentTrack,
  isPlaying,
  onPlay,
  onPause,
  className = ''
}) => {
  const [tracks, setTracks] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [pagination, setPagination] = useState({
    hasMore: false,
    nextToken: null
  })
  const [loadingMore, setLoadingMore] = useState(false)

  const loadTracks = async (nextToken = null) => {
    try {
      const params = {
        limit: 20,
        ...(creatorId && { creatorId }),
        ...(nextToken && { lastEvaluatedKey: nextToken }),
        ...filters // Include all filters from props
      }

      const response = await apiService.listTracks(params)
      
      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to load tracks')
      }

      const newTracks = response.data.tracks || []
      
      if (nextToken) {
        setTracks(prev => [...prev, ...newTracks])
      } else {
        setTracks(newTracks)
      }

      setPagination({
        hasMore: response.data.pagination?.hasMore || false,
        nextToken: response.data.pagination?.nextToken || null
      })

    } catch (error) {
      console.error('Load tracks error:', error)
      setError(error.message)
      toast.error(error.message || 'Failed to load tracks')
    } finally {
      setLoading(false)
      setLoadingMore(false)
    }
  }

  useEffect(() => {
    setLoading(true)
    loadTracks()
  }, [creatorId, filters])

  const handleLoadMore = async () => {
    if (loadingMore || !pagination.hasMore) return
    
    setLoadingMore(true)
    await loadTracks(pagination.nextToken)
  }

  if (loading) {
    return (
      <div className={`flex justify-center py-8 ${className}`}>
        <LoadingSpinner />
      </div>
    )
  }

  if (error) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <p className="text-red-500 mb-4">{error}</p>
        <button
          onClick={() => {
            setError(null)
            setLoading(true)
            loadTracks()
          }}
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
        >
          Try Again
        </button>
      </div>
    )
  }

  if (tracks.length === 0) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <p className="text-gray-500">
          {creatorId ? 'No tracks uploaded yet' : 'No tracks available'}
        </p>
      </div>
    )
  }

  return (
    <div className={className}>
      <div className="space-y-4">
        {tracks.map((track) => (
          <TrackCard
            key={track.trackId}
            track={track}
            isPlaying={isPlaying}
            onPlay={onPlay}
            onPause={onPause}
            currentTrack={currentTrack}
          />
        ))}
      </div>

      {/* Load More Button */}
      {pagination.hasMore && (
        <div className="text-center mt-6">
          <button
            onClick={handleLoadMore}
            disabled={loadingMore}
            className="px-6 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50 flex items-center gap-2 mx-auto"
          >
            {loadingMore && <LoadingSpinner size="sm" />}
            Load More Tracks
          </button>
        </div>
      )}
    </div>
  )
}

export default TrackList
