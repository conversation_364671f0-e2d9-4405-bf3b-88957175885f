const { DynamoDBClient, QueryCommand } = require('@aws-sdk/client-dynamodb')
const { unmarshall } = require('@aws-sdk/util-dynamodb')
const { successResponse, errorResponse } = require('../utils/responseHelper')
const { validateInput } = require('../utils/validation')
const Joi = require('joi')

const dynamoClient = new DynamoDBClient({ region: process.env.AWS_REGION })

// Validation schema for query parameters
const querySchema = Joi.object({
  limit: Joi.number().integer().min(1).max(100).default(20),
  lastEvaluatedKey: Joi.string().allow(''),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc')
})

/**
 * Lambda handler for retrieving track comments
 */
exports.handler = async (event) => {
  console.log('Get track comments request:', JSON.stringify(event, null, 2))

  try {
    // Extract track ID from path parameters
    const trackId = event.pathParameters?.trackId
    if (!trackId) {
      return errorResponse('Track ID is required', 400, 'MISSING_TRACK_ID')
    }

    // Parse and validate query parameters
    const queryParams = event.queryStringParameters || {}
    const validation = validateInput(queryParams, querySchema)
    
    if (!validation.isValid) {
      return errorResponse('Invalid query parameters', 400, 'VALIDATION_ERROR', validation.errors)
    }

    const { limit, lastEvaluatedKey, sortOrder } = validation.value

    // Query comments for the track
    const queryCommand = new QueryCommand({
      TableName: process.env.COMMENTS_TABLE_NAME,
      IndexName: 'TrackIdCreatedAtIndex',
      KeyConditionExpression: 'trackId = :trackId',
      ExpressionAttributeValues: {
        ':trackId': { S: trackId }
      },
      ScanIndexForward: sortOrder === 'asc', // true for ascending, false for descending
      Limit: limit
    })

    // Add pagination if provided
    if (lastEvaluatedKey) {
      try {
        queryCommand.ExclusiveStartKey = JSON.parse(Buffer.from(lastEvaluatedKey, 'base64').toString())
      } catch (error) {
        return errorResponse('Invalid pagination token', 400, 'INVALID_PAGINATION')
      }
    }

    const result = await dynamoClient.send(queryCommand)

    // Process comments and get user information
    const comments = await Promise.all(
      (result.Items || []).map(async (item) => {
        const comment = unmarshall(item)
        
        // Get user information for the comment
        const userInfo = await getUserInfo(comment.userId)
        
        return {
          commentId: comment.commentId,
          content: comment.content,
          createdAt: comment.createdAt,
          updatedAt: comment.updatedAt,
          user: userInfo ? {
            userId: userInfo.userId,
            username: userInfo.username,
            profileImageUrl: userInfo.profileImageUrl
          } : {
            userId: comment.userId,
            username: 'Unknown User',
            profileImageUrl: null
          }
        }
      })
    )

    // Prepare pagination token
    let nextToken = null
    if (result.LastEvaluatedKey) {
      nextToken = Buffer.from(JSON.stringify(result.LastEvaluatedKey)).toString('base64')
    }

    return successResponse({
      trackId,
      comments,
      pagination: {
        limit,
        nextToken,
        hasMore: !!result.LastEvaluatedKey,
        sortOrder
      },
      totalCount: comments.length
    }, 'Comments retrieved successfully')

  } catch (error) {
    console.error('Get track comments error:', error)
    return errorResponse('Failed to retrieve comments', 500, 'RETRIEVAL_ERROR')
  }
}

/**
 * Get user information
 */
async function getUserInfo(userId) {
  try {
    const queryCommand = new QueryCommand({
      TableName: process.env.USERS_TABLE_NAME,
      KeyConditionExpression: 'userId = :userId',
      ExpressionAttributeValues: {
        ':userId': { S: userId }
      },
      ProjectionExpression: 'userId, username, profileImageUrl'
    })

    const result = await dynamoClient.send(queryCommand)
    
    if (result.Items && result.Items.length > 0) {
      return unmarshall(result.Items[0])
    }
    
    return null
  } catch (error) {
    console.warn('Failed to fetch user info for userId:', userId, error)
    return null
  }
}
