const fs = require('fs');
const path = require('path');

// Create a simple test audio file (WAV format)
function createTestAudioFile() {
  const uploadsDir = path.join(__dirname, '../uploads');
  
  // Ensure uploads directory exists
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
  }

  // Create a simple WAV file with a 440Hz tone (A note)
  const sampleRate = 44100;
  const duration = 5; // 5 seconds
  const frequency = 440; // A note
  const amplitude = 0.3;
  
  const numSamples = sampleRate * duration;
  const buffer = Buffer.alloc(44 + numSamples * 2); // WAV header + 16-bit samples
  
  // WAV header
  buffer.write('RIFF', 0);
  buffer.writeUInt32LE(36 + numSamples * 2, 4);
  buffer.write('WAVE', 8);
  buffer.write('fmt ', 12);
  buffer.writeUInt32LE(16, 16); // PCM format chunk size
  buffer.writeUInt16LE(1, 20);  // PCM format
  buffer.writeUInt16LE(1, 22);  // Mono
  buffer.writeUInt32LE(sampleRate, 24);
  buffer.writeUInt32LE(sampleRate * 2, 28); // Byte rate
  buffer.writeUInt16LE(2, 32);  // Block align
  buffer.writeUInt16LE(16, 34); // Bits per sample
  buffer.write('data', 36);
  buffer.writeUInt32LE(numSamples * 2, 40);
  
  // Generate sine wave samples
  for (let i = 0; i < numSamples; i++) {
    const sample = Math.sin(2 * Math.PI * frequency * i / sampleRate) * amplitude;
    const intSample = Math.round(sample * 32767);
    buffer.writeInt16LE(intSample, 44 + i * 2);
  }
  
  // Save test files
  const testFiles = [
    'sample-1.wav',
    'sample-2.wav',
    'test-track.wav'
  ];
  
  testFiles.forEach(fileName => {
    const filePath = path.join(uploadsDir, fileName);
    fs.writeFileSync(filePath, buffer);
    console.log(`✅ Created test audio file: ${filePath}`);
  });
  
  console.log(`🎵 Created ${testFiles.length} test audio files`);
  console.log(`📁 Files saved to: ${uploadsDir}`);
  console.log(`🎼 Audio specs: ${sampleRate}Hz, 16-bit, mono, ${duration}s, ${frequency}Hz tone`);
}

// Run the function
try {
  createTestAudioFile();
} catch (error) {
  console.error('❌ Error creating test audio files:', error.message);
}
