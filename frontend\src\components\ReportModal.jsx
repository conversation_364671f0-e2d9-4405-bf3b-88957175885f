import React, { useState } from 'react'
import { X, Alert<PERSON><PERSON>gle, Flag } from 'lucide-react'
import toast from 'react-hot-toast'

const ReportModal = ({ isOpen, onClose, reportType, targetId, targetInfo }) => {
  const [reason, setReason] = useState('')
  const [description, setDescription] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Report categories based on type
  const contentReportCategories = [
    { value: 'spam', label: 'Spam or misleading content' },
    { value: 'inappropriate', label: 'Inappropriate or offensive content' },
    { value: 'copyright', label: 'Copyright infringement' },
    { value: 'hate_speech', label: 'Hate speech or discrimination' },
    { value: 'violence', label: 'Violence or harmful content' },
    { value: 'misinformation', label: 'False or misleading information' },
    { value: 'other', label: 'Other (please describe)' }
  ]

  const userReportCategories = [
    { value: 'spam', label: 'Spam or unwanted messages' },
    { value: 'harassment', label: 'Harassment or bullying' },
    { value: 'impersonation', label: 'Impersonation or fake account' },
    { value: 'inappropriate_profile', label: 'Inappropriate profile content' },
    { value: 'hate_speech', label: 'Hate speech or discrimination' },
    { value: 'fake_account', label: 'Fake or bot account' },
    { value: 'other', label: 'Other (please describe)' }
  ]

  const categories = reportType === 'content' ? contentReportCategories : userReportCategories

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!reason) {
      toast.error('Please select a reason for reporting')
      return
    }

    if (reason === 'other' && !description.trim()) {
      toast.error('Please provide a description for "Other" reports')
      return
    }

    setIsSubmitting(true)

    try {
      // Prepare API call data
      const apiEndpoint = reportType === 'content'
        ? 'http://localhost:3001/dev/reports/content'
        : 'http://localhost:3001/dev/reports/user'

      const reportData = reportType === 'content'
        ? {
            contentId: targetId,
            contentType: 'track', // Assuming tracks for now
            reason,
            description: description.trim()
          }
        : {
            reportedUserId: targetId,
            reason,
            description: description.trim()
          }

      console.log('Submitting report:', reportData)

      // Make API call
      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(reportData)
      })

      if (response.ok) {
        const result = await response.json()
        toast.success('Report submitted successfully. Thank you for helping keep Tunami safe.')
        console.log('Report submitted:', result)
        onClose()

        // Reset form
        setReason('')
        setDescription('')
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to submit report. Please try again.')
      }
    } catch (error) {
      console.error('Error submitting report:', error)
      toast.error('Failed to submit report. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      setReason('')
      setDescription('')
      onClose()
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <Flag className="h-6 w-6 text-red-600" />
            <h2 className="text-xl font-semibold text-gray-900">
              Report {reportType === 'content' ? 'Content' : 'User'}
            </h2>
          </div>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-6">
          {/* Target Info */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600 mb-2">
              You are reporting:
            </p>
            <p className="font-medium text-gray-900">
              {reportType === 'content' 
                ? `"${targetInfo?.title}" by ${targetInfo?.artist}`
                : `User: ${targetInfo?.username}`
              }
            </p>
          </div>

          {/* Warning */}
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <p className="text-sm text-yellow-800 font-medium mb-1">
                  Important
                </p>
                <p className="text-sm text-yellow-700">
                  False reports may result in action against your account. 
                  Please only report content that violates our community guidelines.
                </p>
              </div>
            </div>
          </div>

          {/* Reason Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Why are you reporting this {reportType === 'content' ? 'content' : 'user'}?
            </label>
            <div className="space-y-2">
              {categories.map((category) => (
                <label key={category.value} className="flex items-start space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name="reason"
                    value={category.value}
                    checked={reason === category.value}
                    onChange={(e) => setReason(e.target.value)}
                    disabled={isSubmitting}
                    className="mt-1 h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
                  />
                  <span className="text-sm text-gray-700">{category.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Description */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Additional details {reason === 'other' ? '(required)' : '(optional)'}
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              disabled={isSubmitting}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed"
              placeholder="Please provide any additional context that would help us understand the issue..."
              maxLength={500}
            />
            <p className="text-xs text-gray-500 mt-1">
              {description.length}/500 characters
            </p>
          </div>

          {/* Actions */}
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || !reason}
              className="flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Submitting...
                </>
              ) : (
                'Submit Report'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default ReportModal
