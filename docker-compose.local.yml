version: '3.8'

services:
  # DynamoDB Local
  dynamodb-local:
    command: "-jar DynamoDBLocal.jar -sharedDb -dbPath ./data"
    image: "amazon/dynamodb-local:latest"
    container_name: tunami-dynamodb-local
    ports:
      - "8000:8000"
    volumes:
      - "./docker/dynamodb:/home/<USER>/data"
    working_dir: /home/<USER>
    environment:
      - AWS_ACCESS_KEY_ID=dummy
      - AWS_SECRET_ACCESS_KEY=dummy
      - AWS_DEFAULT_REGION=us-east-1
    networks:
      - tunami-local

  # LocalStack for S3 and other AWS services
  localstack:
    container_name: tunami-localstack
    image: localstack/localstack:latest
    ports:
      - "4566:4566"            # LocalStack Gateway
      - "4510-4559:4510-4559"  # External services port range
    environment:
      - DEBUG=${DEBUG-}
      - DOCKER_HOST=unix:///var/run/docker.sock
      - SERVICES=s3,cognito-idp
      - AWS_DEFAULT_REGION=us-east-1
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - DATA_DIR=/tmp/localstack/data
    volumes:
      - "./docker/localstack:/tmp/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
    networks:
      - tunami-local

  # DynamoDB Admin (Optional - for GUI management)
  dynamodb-admin:
    image: aaronshaf/dynamodb-admin:latest
    container_name: tunami-dynamodb-admin
    ports:
      - "8001:8001"
    environment:
      - DYNAMO_ENDPOINT=http://dynamodb-local:8000
      - AWS_REGION=us-east-1
      - AWS_ACCESS_KEY_ID=dummy
      - AWS_SECRET_ACCESS_KEY=dummy
    depends_on:
      - dynamodb-local
    networks:
      - tunami-local

networks:
  tunami-local:
    driver: bridge

volumes:
  dynamodb-data:
  localstack-data:
