import React, { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext.jsx'
import { 
  Shield, 
  AlertTriangle, 
  Users, 
  Music, 
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Ban,
  Trash2
} from 'lucide-react'
import toast from 'react-hot-toast'

const AdminDashboard = () => {
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState('overview')
  const [reports, setReports] = useState([])
  const [stats, setStats] = useState({
    totalReports: 0,
    pendingReports: 0,
    resolvedReports: 0,
    totalUsers: 0,
    totalTracks: 0,
    activeUsers: 0
  })
  const [loading, setLoading] = useState(true)

  // Load data from API
  useEffect(() => {
    loadAdminData()
  }, [])

  const loadAdminData = async () => {
    try {
      setLoading(true)

      // Load platform statistics
      const statsResponse = await fetch('http://localhost:3001/dev/admin/stats')
      if (statsResponse.ok) {
        const statsData = await statsResponse.json()
        setStats(statsData)
      }

      // Load reports
      const reportsResponse = await fetch('http://localhost:3001/dev/admin/reports?status=pending')
      if (reportsResponse.ok) {
        const reportsData = await reportsResponse.json()
        setReports(reportsData.reports || [])
      }

      setLoading(false)
    } catch (error) {
      console.error('Error loading admin data:', error)
      toast.error('Failed to load admin data')
      setLoading(false)
    }
  }

  const handleReportAction = async (reportId, action) => {
    try {
      console.log(`Taking action ${action} on report ${reportId}`)

      // Call API to take moderation action
      const response = await fetch('http://localhost:3001/dev/admin/moderation/action', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reportId, action })
      })

      if (response.ok) {
        const result = await response.json()

        // Update local state
        setReports(prev => prev.map(report =>
          report.id === reportId
            ? { ...report, status: 'resolved', action, resolvedAt: new Date().toISOString() }
            : report
        ))

        toast.success(result.message || `Action ${action} completed successfully`)

        // Reload data to get updated stats
        loadAdminData()
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to process action')
      }
    } catch (error) {
      console.error('Error processing action:', error)
      toast.error('Failed to process action')
    }
  }

  const StatCard = ({ icon: Icon, title, value, color = 'blue' }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className={`p-3 rounded-full bg-${color}-100`}>
          <Icon className={`h-6 w-6 text-${color}-600`} />
        </div>
        <div className="ml-4">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-semibold text-gray-900">{value}</p>
        </div>
      </div>
    </div>
  )

  const ReportCard = ({ report }) => {
    const priorityColors = {
      high: 'red',
      medium: 'yellow',
      low: 'green'
    }

    const statusColors = {
      pending: 'yellow',
      resolved: 'green',
      dismissed: 'gray'
    }

    return (
      <div className="bg-white rounded-lg shadow p-6 mb-4">
        <div className="flex justify-between items-start mb-4">
          <div className="flex items-center space-x-3">
            <span className={`px-2 py-1 text-xs font-medium rounded-full bg-${priorityColors[report.priority]}-100 text-${priorityColors[report.priority]}-800`}>
              {report.priority} priority
            </span>
            <span className={`px-2 py-1 text-xs font-medium rounded-full bg-${statusColors[report.status]}-100 text-${statusColors[report.status]}-800`}>
              {report.status}
            </span>
            <span className="text-sm text-gray-500">
              {report.type === 'track' ? 'Track Report' : 'User Report'}
            </span>
          </div>
          <span className="text-sm text-gray-500">
            {new Date(report.reportedAt).toLocaleDateString()}
          </span>
        </div>

        <div className="mb-4">
          <h3 className="font-medium text-gray-900 mb-2">
            Reported {report.type}: {report.target.title || report.target.username}
          </h3>
          <p className="text-sm text-gray-600 mb-2">
            <strong>Reason:</strong> {report.reason}
          </p>
          <p className="text-sm text-gray-600 mb-2">
            <strong>Reporter:</strong> {report.reporter.username} ({report.reporter.email})
          </p>
          {report.description && (
            <p className="text-sm text-gray-600">
              <strong>Description:</strong> {report.description}
            </p>
          )}
        </div>

        {report.status === 'pending' && (
          <div className="flex space-x-2">
            <button
              onClick={() => handleReportAction(report.id, 'dismiss')}
              className="flex items-center px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
            >
              <XCircle className="h-4 w-4 mr-1" />
              Dismiss
            </button>
            <button
              onClick={() => handleReportAction(report.id, 'warn_user')}
              className="flex items-center px-3 py-2 text-sm bg-yellow-100 text-yellow-700 rounded-md hover:bg-yellow-200"
            >
              <AlertTriangle className="h-4 w-4 mr-1" />
              Warn User
            </button>
            {report.type === 'track' && (
              <button
                onClick={() => handleReportAction(report.id, 'remove_content')}
                className="flex items-center px-3 py-2 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Remove Content
              </button>
            )}
            {report.type === 'user' && (
              <button
                onClick={() => handleReportAction(report.id, 'suspend_user')}
                className="flex items-center px-3 py-2 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200"
              >
                <Ban className="h-4 w-4 mr-1" />
                Suspend User
              </button>
            )}
          </div>
        )}

        {report.status === 'resolved' && (
          <div className="bg-green-50 p-3 rounded-md">
            <p className="text-sm text-green-800">
              <strong>Resolved:</strong> {report.action} on {new Date(report.resolvedAt).toLocaleDateString()}
            </p>
          </div>
        )}
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-3 mb-2">
          <Shield className="h-8 w-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
        </div>
        <p className="text-gray-600">Welcome back, {user?.username}. Manage platform content and users.</p>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 mb-8">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: TrendingUp },
            { id: 'reports', label: 'Reports Queue', icon: AlertTriangle },
            { id: 'users', label: 'User Management', icon: Users },
            { id: 'content', label: 'Content Moderation', icon: Music }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Platform Overview</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <StatCard icon={AlertTriangle} title="Pending Reports" value={stats.pendingReports} color="red" />
            <StatCard icon={CheckCircle} title="Resolved Reports" value={stats.resolvedReports} color="green" />
            <StatCard icon={Users} title="Total Users" value={stats.totalUsers} color="blue" />
            <StatCard icon={Music} title="Total Tracks" value={stats.totalTracks} color="purple" />
            <StatCard icon={TrendingUp} title="Active Users" value={stats.activeUsers} color="indigo" />
            <StatCard icon={Clock} title="Total Reports" value={stats.totalReports} color="gray" />
          </div>
        </div>
      )}

      {activeTab === 'reports' && (
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Reports Queue</h2>
          <div className="space-y-4">
            {reports.filter(r => r.status === 'pending').map(report => (
              <ReportCard key={report.id} report={report} />
            ))}
            {reports.filter(r => r.status === 'pending').length === 0 && (
              <div className="text-center py-8">
                <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                <p className="text-gray-500">No pending reports</p>
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === 'users' && (
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-6">User Management</h2>
          <div className="bg-white rounded-lg shadow p-6">
            <p className="text-gray-600">User management tools coming soon...</p>
          </div>
        </div>
      )}

      {activeTab === 'content' && (
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Content Moderation</h2>
          <div className="bg-white rounded-lg shadow p-6">
            <p className="text-gray-600">Content moderation tools coming soon...</p>
          </div>
        </div>
      )}
    </div>
  )
}

export default AdminDashboard
